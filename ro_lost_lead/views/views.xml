<odoo>
  <data>

    <record id="crm.action_mark_as_lost" model="ir.actions.server">
      <field name="code">
action_values = env.ref('crm.crm_lead_lost_action').sudo().read()[0]
action_values.update({'context': env.context})
action = action_values
      </field>
    </record>

    <record id="inherit_view_id_inherit_lead" model="ir.ui.view">
      <field name="model">crm.lead</field>
      <field name="inherit_id" ref="crm.crm_lead_view_form"/>
      <field name="arch" type="xml">

        <xpath expr="//button[@name='action_set_lost']" position="replace">
          <button name="%(crm.crm_lead_lost_action)d" string="Lost" type="action" data-hotkey="l" title="Mark as lost" attrs="{'invisible': ['|', ('type', '=', 'opportunity'), '&amp;', ('probability', '=', 0), ('active', '=', False)]}"/>
        </xpath>

      </field>
    </record>
    
  </data>
</odoo>