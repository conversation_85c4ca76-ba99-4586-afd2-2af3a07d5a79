# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import _, api, fields, models
from odoo.exceptions import UserError

class SaleReceiptWizard(models.TransientModel):
    _name = 'sale.receipt.wizard'
    _description = 'Sale Receipt wizard'

    sale_id = fields.Many2one('sale.order')

    location_id = fields.Many2one('stock.location')

    product_id = fields.Many2one('product.product', domain="[('detailed_type','=','product')]")

    product_qty = fields.Float()
    uom_id = fields.Many2one(
        comodel_name='uom.uom',
        string="Unit of Measure",
        compute='_compute_product_uom',
        store=True, readonly=False, precompute=True, ondelete='restrict',
        domain="[('category_id', '=', product_uom_category_id)]")
    
    product_uom_category_id = fields.Many2one(related='product_id.uom_id.category_id', depends=['product_id'])

    # lot_name = fields.Char()

    @api.model
    def default_get(self, fields):
        res = super().default_get(fields)
        active_id = self._context.get('active_id')
        res["sale_id"] = active_id
        return res

    @api.depends('product_id')
    def _compute_product_uom(self):
        for rec in self:
            if not rec.uom_id or (rec.product_id.uom_id.id != rec.uom_id.id):
                rec.uom_id = rec.product_id.uom_id


    def create_receipt_order(self):
        for rec in self:
            if rec.location_id.warehouse_id:
                source_location_id = rec.location_id.warehouse_id.in_type_id.default_location_src_id.id
                destination_location_id = rec.location_id.id
            else:
                source_location_id = False
                destination_location_id = False
            
            if not source_location_id:
                _customerloc, location_id = self.env['stock.warehouse']._get_partner_locations()
                source_location_id = location_id.id

            # Create a new stock picking order by copying the existing one
            new_picking = self.env['stock.picking'].create({
                'partner_id': rec.sale_id.partner_id.id,
                'origin': rec.sale_id.name,
                'picking_type_id': rec.location_id.warehouse_id.in_type_id.id,
                'location_id': source_location_id,
                'location_dest_id': destination_location_id,
                'receipt_so_id': rec.sale_id.id
            })

            sale_line = rec.sale_id.order_line.filtered(lambda x:x.product_id.detailed_type == 'service')

            if len(sale_line) > 0:
                line = self.env['stock.move'].create({
                    'name': sale_line[0].name,
                    'product_id': rec.product_id.id,
                    'product_uom_qty': rec.product_qty,
                    'product_uom': rec.uom_id.id,
                    'picking_id': new_picking.id,
                    'location_id': source_location_id,
                    'location_dest_id': destination_location_id,
                    # 'ro_so_lot_name': rec.lot_name or rec.sale_id.name,
                })
            else:
                raise UserError("No Service Product found in Sale order")

            new_picking.action_confirm()
            new_picking.action_assign()
            new_picking.action_set_quantities_to_reservation()
            # for sml in new_picking.move_line_ids:
                # sml.lot_name = rec.lot_name or rec.sale_id.name
