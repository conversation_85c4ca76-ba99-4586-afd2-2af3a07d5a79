# -*- coding: utf-8 -*-

from collections import defaultdict
from datetime import datetime, time
from odoo import models, fields, api, _
from odoo.exceptions import UserError


class SaleOrder(models.Model):
    _inherit = "sale.order"
    
    receipt_order_ids = fields.One2many('stock.picking','receipt_so_id')

    receipt_count = fields.Float(compute="get_receipt_count")

    @api.depends('receipt_order_ids')
    def get_receipt_count(self):
        for rec in self:
            rec.receipt_count = len(rec.receipt_order_ids)

    def create_sale_receipt(self):
        view = self.env.ref('ro_receipt_from_so.ro_view_sale_receipt_wizard')
        context = dict(self._context)
        context.update({'active_model': 'sale.order',
                        'active_id': self.id, 'active_ids': self.ids})
        return {
            'name': _('Create Receipt Order'),
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'sale.receipt.wizard',
            'views': [(view.id, 'form')],
            'view_id': view.id,
            'target': 'new',
            'context': context
        }
    

    def open_receipt_orders_view(self):
      self.ensure_one()
      return {
         'type': 'ir.actions.act_window',
         'name': 'Sale Receipts',
         'view_mode': 'tree,form',
         'res_model': 'stock.picking',
         'domain': [('receipt_so_id', '=', self.id)],
         'context': "{'create': False}"
      }      