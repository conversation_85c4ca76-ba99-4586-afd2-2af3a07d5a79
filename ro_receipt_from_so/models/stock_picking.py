from odoo import api, fields, models


class StockPicking(models.Model):
    _inherit = "stock.picking"
    
    receipt_so_id = fields.Many2one('sale.order')
    
    # def button_validate(self):
    #     for rec in self:
    #         move_line_ids = rec.filtered(lambda pick: pick.picking_type_id.code=='incoming').move_line_ids.filtered(lambda line: line.move_id.ro_so_lot_name\
    #                                                                                                                and line.move_id.product_id.tracking == 'lot')
    #         for move_line in move_line_ids:
    #             move_line.lot_name = move_line.move_id.ro_so_lot_name

    #     return super(StockPicking, self).button_validate()


    # def _create_backorder
    
    # def action_assign(self):
    #     rec = super(StockPicking, self).action_assign()
    #     for rec in self:
    #         if rec.receipt_so_id:
    #             for sm in rec.move_ids:
    #                 for sml in sm.move_line_ids:
    #                     sml.lot_name = sm.ro_so_lot_name
