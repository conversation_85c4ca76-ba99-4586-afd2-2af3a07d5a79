<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="product_pricelist_view_tree_level" model="ir.ui.view">
            <field name="model">product.pricelist</field>
            <field name="inherit_id" ref="product.product_pricelist_view"/>
            <field name="arch" type="xml">
                <xpath expr="//page[@name='pricelist_rules']/field/tree[2]/field[@name='price']" position="after">
                    <field name="level1" groups="sales_team.group_sale_manager"/>
                    <field name="level2" groups="sales_team.group_sale_manager"/>
                    <field name="level3" groups="sales_team.group_sale_manager"/>
                    <field name="level4" groups="sales_team.group_sale_manager"/>
                </xpath>
                <xpath expr="//page[@name='pricelist_rules']/field/tree[1]/field[@name='fixed_price']" position="after">
                    <field name="level1" groups="sales_team.group_sale_manager"/>
                    <field name="level2" groups="sales_team.group_sale_manager"/>
                    <field name="level3" groups="sales_team.group_sale_manager"/>
                    <field name="level4" groups="sales_team.group_sale_manager"/>
                </xpath>
            </field>
        </record>

        <record id="product_pricelist_item_form_view_level" model="ir.ui.view">
            <field name="model">product.pricelist.item</field>
            <field name="inherit_id" ref="product.product_pricelist_item_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//sheet/group/group[@name='pricelist_rule_target']/.." position="after">
                    <group name="pricelist_level" groups="sales_team.group_sale_manager" string="Level Commission">
                        <field name="level1"/>
                        <field name="level2"/>
                        <field name="level3"/>
                        <field name="level4"/>
                    </group>
                </xpath>
            </field>
        </record>
    </data>
</odoo>