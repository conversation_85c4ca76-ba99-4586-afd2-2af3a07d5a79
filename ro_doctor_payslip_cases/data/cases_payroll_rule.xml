<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data noupdate="1">
        <record id="hr_salary_rule_doctor_cases" model="hr.salary.rule">
            <field name="name">Dr <PERSON>s</field>
            <field name="category_id" ref="hr_payroll.ALW"/>
            <field name="code">CASES</field>
            <field name="sequence" eval="5"/>
            <field name="struct_id" ref="hr_payroll.structure_002"/>
            <field name="amount_select">code</field>
            <field name="amount_python_compute">result = employee.get_cases_payrule(payslip.date_from, payslip.date_to)</field>
        </record>
    </data>
</odoo>