# -*- coding: utf-8 -*-
{
    'name': "ro doctor payslip cases",
    
    'description':"""
    	Calculate doctor cases in payslip from lines linked with their analytic account based on their level's amount in the pricelist (from invoices)
    """,
    'summary': """
        Employee payrule based on pricelist and analytic lines
    """,

    'author': "Roaya",
    'website': "https://www.roayadm.com",
    'category': 'Employee',
    'license': 'OPL-1',

    # any module necessary for this one to work correctly
    'depends': ['hr', 'sale_management','analytic','product','hr_payroll'],

    # always loaded
    'data': [
        'data/cases_payroll_rule.xml',
        'views/product_pricelist_views.xml',
        'views/hr_employee_views.xml',
        'views/analytic_account_views.xml',
    ],
}
