from odoo import models, fields, api
from odoo.exceptions import MissingError, ValidationError


class HrEmployee(models.Model):
    _inherit = "hr.employee"

    # region FIELDS
    level = fields.Selection([('level1', 'Level 1'), ('level2', 'Level 2'),
                              ('level3', 'Level 3'), ('level4', 'Level 4')])

    analytic_account_id = fields.Many2one('account.analytic.account')

    # endregion

    @api.constrains('analytic_account_id')
    def _check_employee_id(self):
        if len(self.analytic_account_id.employee_ids) > 1:
            raise ValidationError(f'Error: the analytic account ({self.analytic_account_id.name}) have already been assigned to employee ({self.analytic_account_id.employee_ids[1].name})')

    def get_cases_payrule(self, date_from, date_to):
        for rec in self:
            result = 0

            if not rec.analytic_account_id:
                return result
            if not rec.analytic_account_id.line_ids:
                return result
            if not rec.level:
                raise MissingError(f'The Employee {rec.name} has analytic account, but no level')

            level = rec.level
            analytic_lines = rec.analytic_account_id.line_ids.filtered(lambda x: (x.date >= date_from) and (x.date <= date_to))

            for line in analytic_lines:
                pricelist_id = line.move_line_id.move_id.pricelist_id

                line_level_price = rec.env['product.pricelist.item'].search_read([('pricelist_id', '=', pricelist_id.id), ('product_tmpl_id','=', line.product_id.product_tmpl_id.id)], ['product_tmpl_id', level])
                if len(line_level_price) != 1:
                    raise ValidationError(f'The product ({line.product_id.name}) is registered ({len(line_level_price)}) times in pricelist ({pricelist_id.name})')
                result += line_level_price[0][level] * line.move_line_id.quantity

            return result
