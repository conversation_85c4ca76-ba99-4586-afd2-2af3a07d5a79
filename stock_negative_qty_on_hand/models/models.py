# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

import logging
_logger = logging.getLogger(__name__)

class stockQuant(models.Model):

    _inherit = 'stock.quant'


    @api.constrains('quantity','company_id')
    def _check_quantity(self):
        for this in self:
            quantity = this.quantity
            location_id = this.location_id

            if quantity < 0  and location_id.usage == 'internal':
                raise ValidationError(_('Not enough quantity To Proceed in {}.'.format(this.product_id.name)))
