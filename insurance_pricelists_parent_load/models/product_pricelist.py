# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import RedirectWarning, UserError, ValidationError, AccessError


class ProductPricelist(models.Model):

    _inherit = 'product.pricelist'

    is_insurance = fields.Boolean()
    parent_company = fields.Many2one('res.partner')
    # partner_percent = fields.Float()

    load_on = fields.Selection([('after_dis', 'After Discount'), (
        'before_dis', 'Before Discount')], default="before_dis", required=True)

    # add_manual = fields.Boolean()
