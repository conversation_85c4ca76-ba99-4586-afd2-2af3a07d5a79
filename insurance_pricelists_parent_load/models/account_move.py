# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import RedirectWarning, UserError, ValidationError, AccessError


class AccountMove(models.Model):

    _inherit = 'account.move'

    parent_id = fields.Many2one('res.partner', string='Company', readonly=True)
    client_amt = fields.Float(string='Clent Load amt', readonly=True)
    load_amt = fields.Float(string='Company Load amt', readonly=True)
    entry_id = fields.Many2one(
        'account.move', string='Entry', copy=False, readonly=True)
    pricelist_id = fields.Many2one(
        'product.pricelist', string='Pricelist', copy=False)

    load_percent = fields.Float()
    
    def button_draft(self):
        res = super(AccountMove, self).button_draft()
        for rec in self:
            if rec.entry_id:
                rec.entry_id.button_draft()
                rec.entry_id.unlink()
        return res
    
    def button_cancel(self):
        res = super(Account<PERSON>ove, self).button_cancel()
        for rec in self:
            if rec.entry_id:
                rec.entry_id.button_draft()
                rec.entry_id.unlink()
        return res


    def action_post(self):
        res = super(AccountMove, self).action_post()

        invoices_and_cn = self.filtered(lambda doc:not doc.entry_id and doc.move_type in ['out_invoice', 'out_refund'] and doc.load_amt > 0)

        for this in invoices_and_cn:
            journal_id = self.env.ref('insurance_pricelists_parent_load.pricelist_journal')
            # self.env['account.journal'].search(
            #     [('name', '=', 'Pricelist')])
            if len(journal_id) == 0:
                raise ValidationError(
                    'Need to add Journal with name Pricelist')

            # if this.pricelist_id.add_manual:
            partner_load = (100 - this.load_percent)
            # else:
            #     partner_load = this.pricelist_id.partner_percent 

            if this.move_type in ('out_invoice') and this.load_amt != 0:
                move = {
                    'name': '/',
                    'journal_id': journal_id[0].id,
                    'date': this.invoice_date,
                    'move_type': 'entry',
                    'ref': this.name + ', ' +this.invoice_origin,
                    'line_ids': [(0, 0, {
                            'name': this.partner_id.name,
                            'debit': this.load_amt,
                            'account_id': this.parent_id.property_account_receivable_id.id,
                            'partner_id': this.parent_id.id,
                    }), (0, 0, {
                        'name': this.partner_id.name,
                        'credit': this.load_amt,
                        'account_id': this.partner_id.property_account_receivable_id.id,
                        'partner_id': this.partner_id.id,
                    })]
                }
                this.entry_id = self.env['account.move'].create(move)
                this.entry_id._post()

            elif this.move_type in ('out_refund'):
                # for credit note
                if this.pricelist_id and this.pricelist_id.load_on == 'after_dis':
                    if partner_load > 100:
                        raise ValidationError(
                            "Check on the Percent It's > 100")
                    this.client_amt = this.amount_total * \
                        ((100 - partner_load) / 100)
                    this.load_amt = this.amount_total - this.client_amt

                elif this.pricelist_id and this.pricelist_id.load_on == 'before_dis':
                    if partner_load > 100:
                        raise ValidationError(
                            "Check on the Percent It's > 100")
                    this.client_amt = (sum([a * b for a, b in zip(this.invoice_line_ids.mapped('quantity'), this.invoice_line_ids.mapped(
                        'price_unit'))])) * ((100 - partner_load) / 100)
                    this.load_amt = this.amount_total - this.client_amt

                move = {
                    'name': '/',
                    'journal_id': journal_id[0].id,
                    'date': this.invoice_date,
                    'ref': this.name +', ' +this.invoice_origin,

                    'line_ids': [(0, 0, {
                        'name': this.partner_id.name,
                        'credit': this.load_amt,
                        'account_id': this.parent_id.property_account_receivable_id.id,
                        'partner_id': this.parent_id.id,
                    }), (0, 0, {
                        'name': this.partner_id.name,
                        'debit': this.load_amt,
                        'account_id': this.partner_id.property_account_receivable_id.id,
                        'partner_id': this.partner_id.id,
                    })]
                }
                this.entry_id = self.env['account.move'].create(move)
                this.entry_id._post()

            # Auto reconcile
            if this.entry_id:

                move_lines = this.entry_id.line_ids
                for line in move_lines:
                    this.js_assign_outstanding_line(line.id)

        return res

    def add_payment_from_parent(self):
        for this in self:
            if not this.entry_id and this.move_type in ('out_invoice', 'out_refund'):
                journal_id = self.env['account.journal'].search(
                    [('name', '=', 'Pricelist')])
                if len(journal_id) == 0:
                    raise ValidationError(
                        'Need to add Journal with name Pricelist')

                # if this.pricelist_id.add_manual:
                partner_load = (100 - this.load_percent)
                # else:
                #     partner_load = this.pricelist_id.partner_percent 

                if this.move_type in ('out_invoice'):
                    move = {
                        'name': '/',
                        'journal_id': journal_id[0].id,
                        'date': this.invoice_date,
                        'ref': this.name +', ' +this.invoice_origin,

                        'line_ids': [(0, 0, {
                            'name': this.partner_id.name,
                            'debit': this.load_amt,
                            'account_id': this.parent_id.property_account_receivable_id.id,
                            'partner_id': this.parent_id.id,
                        }), (0, 0, {
                            'name': this.partner_id.name,
                            'credit': this.load_amt,
                            'account_id': this.partner_id.property_account_receivable_id.id,
                            'partner_id': this.partner_id.id,
                        })]
                    }
                    this.entry_id = self.env['account.move'].create(move)
                    this.entry_id._post()

                elif this.move_type in ('out_refund'):
                    # for credit note
                    if this.pricelist_id and this.pricelist_id.load_on == 'after_dis':
                        if partner_load > 100:
                            raise ValidationError(
                                "Check on the Percent It's > 100")
                        this.client_amt = this.amount_total * \
                            ((100 - partner_load) / 100)
                        this.load_amt = this.amount_total - this.client_amt

                    elif this.pricelist_id and this.pricelist_id.load_on == 'before_dis':
                        if partner_load > 100:
                            raise ValidationError(
                                "Check on the Percent It's > 100")
                        this.client_amt = (sum([a * b for a, b in zip(this.invoice_line_ids.mapped('quantity'), this.invoice_line_ids.mapped(
                            'price_unit'))])) * ((100 - partner_load) / 100)
                        this.load_amt = this.amount_total - this.client_amt

                    move = {
                        'name': '/',
                        'journal_id': journal_id[0].id,
                        'date': this.invoice_date,
                        'ref': this.name +', ' +this.invoice_origin,

                        'line_ids': [(0, 0, {
                            'name': this.partner_id.name,
                            'credit': this.load_amt,
                            'account_id': this.parent_id.property_account_receivable_id.id,
                            'partner_id': this.parent_id.id,
                        }), (0, 0, {
                            'name': this.partner_id.name,
                            'debit': this.load_amt,
                            'account_id': this.partner_id.property_account_receivable_id.id,
                            'partner_id': this.partner_id.id,
                        })]
                    }
                    this.entry_id = self.env['account.move'].create(move)
                    this.entry_id._post()
