# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import RedirectWarning, UserError, ValidationError, AccessError


class SaleOrder(models.Model):

    _inherit = 'sale.order'

    extra_client_load = fields.Float('Extra Client Laod')

    load_percent = fields.Float()
    max_load_amt = fields.Float()

    # add_manual = fields.Boolean(related='pricelist_id.add_manual')
    is_insurance = fields.Boolean(related='pricelist_id.is_insurance')

    approval_num = fields.Char()
    insurance_num = fields.Char()

    def action_confirm(self):
        for this in self:
            if this.is_insurance:
                if not this.approval_num:
                    raise ValidationError(_('Need to add approval number'))
                elif not this.insurance_num:
                    raise ValidationError(_('Need to add Insurance number'))
                elif not this.validity_date:
                    raise ValidationError(_('Need to add Expiration Date'))
            return super(SaleOrder, this).action_confirm()


    @api.constrains('load_percent')
    def load_percent_check(self):
        for this in self:
            if this.load_percent > 100 or this.load_percent < 0:
                raise ValidationError('Load percentage must be between 0 & 100')

    @api.constrains('extra_client_load')
    def check_value_of_extra_load(self):
        for this in self:
            if this.extra_client_load:
                if this.total_parent_load != 0 and this.total_parent_load < 0 :
                    raise ValidationError(_("Extra client load can't be more than Parent load"))
                if this.extra_client_load < 0:
                    raise ValidationError(_("Extra client load can't < 0"))

    total_client_load = fields.Float(compute="get_total_parent_load")
    total_parent_load = fields.Float(compute="get_total_parent_load")
    
    total_amount_before_discount = fields.Float('Total Price', compute='get_total_parent_load')

    @api.depends('order_line','extra_client_load')
    def get_total_parent_load(self):
        for this in self:
            this.total_client_load = 0
            this.total_parent_load = 0

            total_price = 0
            
            if this.order_line:
                this.total_client_load = sum(
                    this.order_line.mapped('client_amt'))

                this.total_parent_load = sum(
                    this.order_line.mapped('load_amt'))

                #check to add the Extra amount to client
                if this.extra_client_load:
                    this.total_parent_load -= this.extra_client_load
                    this.total_client_load += this.extra_client_load
                
                #To Be the Max amount
                if this.max_load_amt > 0 and this.amount_total > this.max_load_amt:
                    load_amt = this.max_load_amt 
                    # * (this.load_percent / 100)

                    this.total_client_load += this.total_parent_load - load_amt
                    this.total_parent_load = load_amt
                    # this.max_load_amt * (this.load_percent / 100)

                for rec in this.order_line:
                    total_price += (rec.price_unit * rec.product_uom_qty)

            this.total_amount_before_discount = total_price

    def _prepare_invoice(self):
        res = super(SaleOrder, self)._prepare_invoice()
        res['parent_id'] = self.pricelist_id.parent_company.id
        res['client_amt'] = self.total_client_load
        res['load_amt'] = self.total_parent_load
        res['pricelist_id'] = self.pricelist_id.id
        res['load_percent'] = self.load_percent
        return res

 
class SaleOrderLine(models.Model):

    _inherit = 'sale.order.line'

    client_amt = fields.Float(compute="get_load_amt")
    load_amt = fields.Float(compute="get_load_amt")

    @api.depends('product_id', 'price_total', 'product_uom_qty', 'price_unit')
    def get_load_amt(self):
        for this in self:
            this.client_amt = 0
            this.load_amt = 0

            # if this.order_id.pricelist_id.add_manual:
            partner_load = this.order_id.load_percent
            # else:
            #     partner_load = this.order_id.pricelist_id.partner_percent 

            if this.product_id and this.order_id and this.order_id.pricelist_id and this.order_id.pricelist_id.load_on == 'after_dis':
                if partner_load > 100:
                    raise ValidationError("Check on the Percent It's > 100")
                if partner_load == 0:
                    this.client_amt = this.price_total 
                else:
                    this.client_amt = this.price_total * \
                        ((100 - partner_load) / 100)
                    this.load_amt = this.price_total - this.client_amt

            elif this.product_id and this.order_id and this.order_id.pricelist_id and this.order_id.pricelist_id.load_on == 'before_dis':
                if partner_load > 100:
                    raise ValidationError("Check on the Percent It's > 100")
                if partner_load == 0:
                    this.client_amt = this.price_total 
                else:
                    this.client_amt = (this.product_uom_qty*this.price_unit) * \
                        ((100 - partner_load) / 100)
                    this.load_amt = this.price_total - this.client_amt
