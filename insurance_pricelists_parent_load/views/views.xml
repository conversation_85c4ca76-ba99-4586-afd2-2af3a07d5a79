<odoo>
    <data>
        <!-- inhert account form view -->
        <record id="view_move_form_parent_load" model="ir.ui.view">
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <!-- <xpath expr="//header" position="inside">
                    <button name="add_payment_from_parent" string="Add parent Entry" class="oe_highlight" type="object" attrs="{'invisible': ['|', '|', ('state', '!=', 'post'), ('auto_post', '=', True), ('move_type', '!=', 'entry')]}"/>
                </xpath> -->
                <xpath expr="//group[@name='sale_info_group']" position="inside">
                    <field name="parent_id" string="Parent Company"/>
                    <field name="client_amt" string="Client Load" />
                    <field name="load_amt" string="Load amount"/>
                    <field name="entry_id" string="Load entry"/>
                    <field name="pricelist_id" />
                </xpath>
            </field>
        </record>


        <record id="view_sale_form_parent_load" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='payment_term_id']" position="after">
                    <field name="is_insurance" invisible='1'/>

                    <field name="load_percent" string="Parent Load" attrs="{'invisible':[('is_insurance','=',False)],'readonly':[('state','not in',('draft','sent'))]}"/>
                    <field name="max_load_amt" string="Parent Load Amt " attrs="{'invisible':[('is_insurance','=',False)],'readonly':[('state','not in',('draft','sent'))]}"/>

                    <field name="approval_num" attrs="{'invisible':[('is_insurance','=',False)],'readonly':[('state','not in',('draft','sent'))]}"/>
                    <field name="insurance_num" attrs="{'invisible':[('is_insurance','=',False)],'readonly':[('state','not in',('draft','sent'))]}"/>
                </xpath>
                <xpath expr="//field[@name='validity_date']" position="attributes">
                    <attribute name="attrs">{'required': [('is_insurance','=',True)],'invisible': [['state', 'in', ['sale', 'done']]], 'readonly': [['state', 'in', ['cancel', 'sale', 'done']]]}</attribute>
                </xpath>

                <xpath expr="//group[@name='sale_total']" position="inside">
                    <field name="total_amount_before_discount" string="Total Price"/>
                    
                    <field name="total_client_load" string="Total Client Load"/>
                    <field name="total_parent_load" string="Total Parent Load"/>
                </xpath>

                <xpath expr="//field[@name='order_line']//tree/field[@name='price_total']" position="after">
                    <field name="client_amt" string="Client Load" optional="hide"/>
                    <field name="load_amt" string="Parent Load" optional="hide"/>
                </xpath>
            </field>
        </record>

        
    </data>
</odoo>