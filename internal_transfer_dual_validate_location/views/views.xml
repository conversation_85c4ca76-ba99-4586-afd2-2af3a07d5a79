<odoo>
    <data>
        <record model="ir.ui.view" id="stock_picking_form_inherit">
            <field name="name">stock picking form view inherit</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="button_validate" string="Second Validate"
                    type="object" class="oe_highlight" attrs="{'invisible':[('state','!=','validate2')]}"/>
                </xpath>
            </field>
        </record>
        <record model="ir.ui.view" id="stock_move_line_search_validate2">
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.stock_move_line_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='product_id']" position="replace">
                    <field name="picking_id" string="Transfer"/>
                </xpath>
                <xpath expr="//field[@name='picking_id']" position="replace">
                    <field name="product_id"/>
                </xpath>
            </field>
        </record>
        <record model="ir.ui.view" id="stock_picking_search_validate2">
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_internal_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='available']" position="after">
                    <filter name="available2" string="Second Validate" domain="[('state', '=', 'validate2')]"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
