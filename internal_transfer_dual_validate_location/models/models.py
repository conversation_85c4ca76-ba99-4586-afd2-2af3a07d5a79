# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, SUPERUSER_ID

from odoo.osv import expression
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.tools.float_utils import float_compare, float_is_zero, float_round
from odoo.exceptions import UserError
from collections import defaultdict
from odoo.addons import stock


class PickingTypeEdit(models.Model):
    _inherit = 'stock.picking'

    state = fields.Selection(selection_add=[
        ('validate2', 'Second Validate'),
        ('done',)])

    flag = fields.Boolean(default=True, copy=False)

    def action_revalidate(self):
        if self.picking_type_id.code == 'internal':
            self.state = 'assigned'

    @api.model
    def create(self, values):
        res = super(PickingTypeEdit, self).create(values)

        if res.picking_type_code == 'internal':
            res.partner_id = False

            mail_activity_data = []

            for user in res.location_id.location_user_ids:
                data = {
                    'res_id': res.id,
                    'res_model_id': self.env.ref('stock.model_stock_picking').id,
                    'user_id': user.id,
                    'summary': 'Product Request Schedule',
                    'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                    'date_deadline': fields.Date.today()
                }
                mail_activity_data.append(data)

            self.env['mail.activity'].with_user(SUPERUSER_ID).create(mail_activity_data)

        return res

    def button_validate(self):
        if self.picking_type_id.code == 'internal' and self.flag:
            # self.env.user.has_group('base.group_system') or 
            # if self.env.user.id == self.user_id.id and self.env.user in self.warehouse_team_from_id.member_ids:
            location_user = self.move_line_ids_without_package.location_id.mapped('location_user_ids')
            dest_user = self.move_line_ids_without_package.location_dest_id.mapped('location_user_ids')
            # self.env.user.has_group('base.group_system') or
            if self.env.user in location_user or len(location_user) == 0:
                self.flag = False
                self.state = 'validate2'
                mail_activity_data = []
                self.activity_ids.with_user(SUPERUSER_ID).action_done()

                for user in dest_user:
                    data = {
                        'res_id': self.id,
                        'res_model_id': self.env.ref('stock.model_stock_picking').id,
                        'user_id': user.id,
                        'summary': 'Validation Arrival Schedule',
                        'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                        'date_deadline': fields.Date.today(),
                    }
                    mail_activity_data.append(data)

                self.env['mail.activity'].with_user(SUPERUSER_ID).create(mail_activity_data)
            else:
                raise UserError(
                    _("You cannot validate. Please wait for the other employee to approve ."))

        elif self.picking_type_id.code == 'internal' and not self.flag:
            dest_user = self.move_line_ids_without_package.location_dest_id.mapped('location_user_ids')
            # self.env.user.has_group('base.group_system') or
            if self.env.user in dest_user or len(dest_user) == 0:
                result = super(PickingTypeEdit, self).button_validate()
                if result == True:
                    self.activity_ids.with_user(SUPERUSER_ID).action_done()
                return result

            else:
                raise UserError(
                    _("You cannot validate. Please wait for the other employee to approve ."))

        else:
            # if self.env.user.id != self.user_id.id:
            return super(PickingTypeEdit, self).button_validate()
            # else:
            #     raise UserError(
            #         _("You cannot validate. Please wait for the other employee to approve ."))


@api.depends('move_type', 'immediate_transfer', 'move_ids.state', 'move_ids.picking_id')
def _compute_state(self):
    ''' State of a picking depends on the state of its related stock.move
    - Draft: only used for "planned pickings"
    - Waiting: if the picking is not ready to be sent so if
        - (a) no quantity could be reserved at all or if
        - (b) some quantities could be reserved and the shipping policy is "deliver all at once"
    - Waiting another move: if the picking is waiting for another move
    - Ready: if the picking is ready to be sent so if:
        - (a) all quantities are reserved or if
        - (b) some quantities could be reserved and the shipping policy is "as soon as possible"
    - Done: if the picking is done.
    - Cancelled: if the picking is cancelled
    '''
    picking_moves_state_map = defaultdict(dict)
    picking_move_lines = defaultdict(set)
    for move in self.env['stock.move'].search([('picking_id', 'in', self.ids)]):
        picking_id = move.picking_id
        move_state = move.state
        picking_moves_state_map[picking_id.id].update({
            'any_draft': picking_moves_state_map[picking_id.id].get('any_draft', False) or move_state == 'draft',
            'all_cancel': picking_moves_state_map[picking_id.id].get('all_cancel', True) and move_state == 'cancel',
            'all_cancel_done': picking_moves_state_map[picking_id.id].get('all_cancel_done',
                                                                          True) and move_state in (
                                   'cancel', 'done'),
            'all_done_are_scrapped': picking_moves_state_map[picking_id.id].get('all_done_are_scrapped', True) and (
                move.scrapped if move_state == 'done' else True),
            'any_cancel_and_not_scrapped': picking_moves_state_map[picking_id.id].get('any_cancel_and_not_scrapped',
                                                                                      False) or (
                                                   move_state == 'cancel' and not move.scrapped),
        })
        picking_move_lines[picking_id.id].add(move.id)
    for picking in self:
        picking_id = (picking.ids and picking.ids[0]) or picking.id
        if not picking_moves_state_map[picking_id]:
            picking.state = 'draft'
        elif picking_moves_state_map[picking_id]['any_draft']:
            picking.state = 'draft'
        elif picking_moves_state_map[picking_id]['all_cancel']:
            picking.state = 'cancel'
        elif picking_moves_state_map[picking_id]['all_cancel_done']:
            if picking_moves_state_map[picking_id]['all_done_are_scrapped'] and picking_moves_state_map[picking_id][
                'any_cancel_and_not_scrapped']:
                picking.state = 'cancel'
            else:
                picking.state = 'done'
        elif picking.state == 'validate2':
            continue
        else:
            relevant_move_state = self.env['stock.move'].browse(
                picking_move_lines[picking_id])._get_relevant_state_among_moves()
            if picking.immediate_transfer and relevant_move_state not in ('draft', 'cancel', 'done'):
                picking.state = 'assigned'
            elif relevant_move_state == 'partially_available':
                picking.state = 'assigned'
            else:
                picking.state = relevant_move_state


stock.models.stock_picking.Picking._compute_state = _compute_state
