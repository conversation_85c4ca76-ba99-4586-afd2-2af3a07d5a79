<odoo>
  <data>

    <record id="inherit_crm_lead_view_form" model="ir.ui.view">
      <field name="name">crm.lead.view.form.inherit</field>
      <field name="model">crm.lead</field>
      <field name="inherit_id" ref="crm.crm_lead_view_form"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='user_id']" position="after">
              <field name="crm_product_template_id" attrs="{'readonly': [['ro_readonly_stage', '=', True]]}" force_save="1"/>
          </xpath>
          <xpath expr="//field[@name='user_id']" position="attributes">
            <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
            <attribute name="force_save">1</attribute>
          </xpath>
      </field>
    </record>

    <record id="crm_product_template_view_tree" model="ir.ui.view">
      <field name="name">crm.product.template</field>
      <field name="model">crm.product.template</field>
      <field name="arch" type="xml">
        <list string="Product Template">
            <field name="name" />
        </list>
      </field>
    </record>

    <record id="crm_product_template_view_form" model="ir.ui.view">
      <field name="name">crm.product.template</field>
      <field name="model">crm.product.template</field>
      <field name="arch" type="xml">
        <form string="Product Template">
          <sheet>
            <group>
                <field name="name" />
            </group>
            
            <group>

              <field  name="crm_lines"
                      mode="list,kanban"
                      widget="section_and_note_one2many"                      
               >
                   <list string="Sales Order Lines"
                         editable="bottom">
                       <control>
                           <create name="add_product_control" string="Add a product"/>
                           <create name="add_note_control" string="Add a note" context="{'default_display_type': 'line_note'}"/>
                           <create name="add_section_control" string="Add a section" context="{'default_display_type': 'line_section'}"/>
                       </control>
                       <field name="sequence" widget="handle"/>

                       <field name="product_id" attrs="{'required': [('display_type', '=', False)],}" domain="[('sale_ok', '=', True)]"/>
                       <field name="name" widget="section_and_note_text" />
                       <field name="quantity" />
                       <field name="uom_id" />
                      <field name="display_type" invisible="1"/>
                      <field name="product_uom_category_id" invisible="1"/>
                      </list>
               </field>

            </group>

          </sheet>
        </form>
      </field>
    </record>

    <record id="crm_product_template_action" model="ir.actions.act_window">
      <field name="name">CRM Product Template</field>
      <field name="res_model">crm.product.template</field>
      <field name="view_mode">list,form</field>
    </record>

    <menuitem
      id="crm_product_template_menu"
      name="Crm Product Template"
      parent="crm.crm_menu_config"
      action="crm_product_template.crm_product_template_action"
    />

  </data>
</odoo>