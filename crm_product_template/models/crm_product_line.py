
from odoo import models, fields,api


class CrmProductLine(models.Model):
    _name = 'crm.lead.product.line'
    _description = 'Crm Lead Product Line'

    sequence = fields.Integer(string='Sequence', default=10)

    product_id = fields.Many2one('product.product',string="Product")
    quantity = fields.Float('Qty')
    name = fields.Char(string="Description")
    uom_id = fields.Many2one('uom.uom', string='uom', related='product_id.uom_id')
    crm_product_order_id = fields.Many2one('crm.product.template', string="Crm Product Template")
    display_type = fields.Selection([
        ('line_section', "Section"),
        ('line_note', "Note")], default=False, help="Technical field for UX purpose.")
    product_uom_category_id = fields.Many2one(related='product_id.uom_id.category_id')

    @api.onchange('product_id')
    def _onchange_(self):
        for rec in self:
            rec.name = rec.product_id.get_product_multiline_description_sale()