from odoo import models, fields,api

class CrmLead(models.Model):
    _inherit = 'crm.lead'

    crm_product_template_id = fields.Many2one('crm.product.template',string="Product Template")
    
    @api.onchange('crm_product_template_id')
    def _onchange_(self):
        for rec in self:
            rec.crm_line = [(5,0,0)]
            for line in rec.crm_product_template_id.crm_lines: 
                rec.crm_line = [(0,0,{
                    'sequence': line.sequence,
                    'product_id': line.product_id.id if line.product_id else False,
                    'name': line.name,
                    'product_uom': line.uom_id.id,
                    'product_uom_qty': line.quantity,
                    'display_type': line.display_type,
                })]

            # for line in rec.crm_line:
            #     line.product_id_change()
