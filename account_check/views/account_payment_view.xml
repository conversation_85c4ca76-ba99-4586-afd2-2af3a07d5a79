<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- TODO inherit other form views also (like check printing module) -->
    <record id="view_account_payment_form_inherited" model="ir.ui.view">
        <field name="name">account.payment.form.inherited</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_form"/>
        <field name="arch" type="xml">
            <form>
                <field name="check_type" invisible="1"/>
                <!-- <field name="check_subtype" invisible="1"/> -->
                <!-- <field name="checkbook_block_manual_number" invisible="1"/> -->
            </form>

            <div name="button_box" position="inside">
                <button class="oe_stat_button" type="object" name="get_checks"
                        icon="fa-bars">
                    <field string="Checks" name="check_count" widget="statinfo"/>
                </button>
            </div>

            <!-- TOD<PERSON> hacer invisible si ('payment_type', '!=', 'transfer') -->
            <xpath expr="//field[@name='ref']" position="after">
                <div colspan="2"
                     attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'delivered_third_check', 'issue_check'])]}">
                    <group name="checks">
                        <field name="check_id" attrs="{'invisible': [('check_id', '=', False)]}"/>
                        <field name="check_ids_copy"
                               attrs="{'invisible': ['|',  '|', ('check_ids_copy', '=', []), ('check_id', '!=', False), ('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])]}">
                            <list edit="false" create="false">
                                <field name="name"/>
                                <field name="payment_date"/>
                                <field name="amount" sum="Total"/>
                                <field name="state" invisible="1"/>
                            </list>
                        </field>
                        <field name="check_ids"
                               options="{'no_create': True}"
                               domain="[('journal_id', '=', journal_id), ('state', '=', 'holding'), ('type', '=', 'third_check')]"
                               attrs="{'invisible': ['|', ('payment_method_code', '!=', 'delivered_third_check'), ('payment_type', 'not in', ['transfer', 'outbound'])], 'required': [('payment_method_code', '=', 'delivered_third_check'), ('payment_type', 'in', ['transfer', 'outbound'])]}">
                            <list edit="false">
                                <field name="name"/>
                                <field name="payment_date"/>
                                <field name="amount" sum="Total"/>
                                <field name="state" invisible="1"/>
                            </list>
                        </field>
                    </group>
                    <group name="check_data" states='draft'>
                        <newline/>
                        <!-- <field name="checkbook_id"
                               attrs="{'invisible': [('payment_method_code', 'not in', ['issue_check'])], 'required': [('payment_method_code', 'in', ['issue_check'])]}"
                               domain="[('journal_id', '=', journal_id), ('state', '=', 'active')]"/> -->
                        <!-- we show this field if check cant be set manually -->
                        <!--<field name="check_number_readonly" attrs="{'invisible': [('checkbook_block_manual_number', '!=', True)]}"/>-->
                        <field name="check_number" force_save="1"
                               attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])], 'required': [('payment_method_code', 'in', ['received_third_check', 'received_safty_check']),('state','=','draft')],  'readonly': [('state', '!=', 'draft')]}"/>
                        <!-- <field name="check_number_id" options="{'no_create': True, 'no_create_edit':True}"
                               attrs="{'invisible': [('payment_method_code', '!=', 'issue_check')], 'required': [('payment_method_code', '=', 'issue_check')]}"/> -->
                        <field name="check_name"
                               attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])], 'required': [('payment_method_code', 'in', ['received_third_check', 'received_safty_check', 'issue_check'])]}"/>
                        <field name="check_issue_date"
                               attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])], 'required': [('payment_method_code', 'in', ['received_third_check', 'received_safty_check', 'issue_check'])]}"/>
                        <field name="check_payment_date"
                               attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])], 'required': [('payment_method_code', 'in', ['issue_check'])]}"/>
                               <!-- , 'required': [('check_subtype', '=', 'deferred')] -->
                        <field name="check_bank_id"
                               attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])], 'required' :[('payment_method_code', 'in', ['received_third_check', 'received_safty_check', 'issue_check'])], 'required': [('payment_method_code', 'in', ['received_third_check'])]}"/>
                        <!--                        <field name="check_owner_vat"-->
                        <!--                               attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check'])],  'required': [('payment_method_code', 'in', ['received_third_check'])]}"/>-->
                        <field name="check_owner_name"
                               attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])],  'required': [('payment_method_code', 'in', ['received_third_check', 'received_safty_check'])]}"/>
                        <!-- TODO add to domain? -->
                        <!-- ('currency_id', '=', currency_none_same_company_id), -->
                        <!-- ('check_deposit_id', '=', False), -->
                        <!-- ('debit', '>', 0), -->
                        <!-- ('reconcile_id', '=', False), -->
                        <!-- ('account_id', '=', journal_default_account_id) -->
                        <!-- TODO use  -->
                    </group>
                </div>
            </xpath>
            <label for="amount" position="before">
                <label for="readonly_amount"
                       attrs="{'invisible': [('payment_method_code', '!=', 'delivered_third_check')]}"/>
                <div name="readonly_amount_div" class="o_row"
                     attrs="{'invisible': [('payment_method_code', '!=', 'delivered_third_check')]}">
                    <field name="readonly_amount"/>
                    <field name="readonly_currency_id" groups="base.group_multi_currency"/>
                </div>
            </label>
            <label for="amount" position="attributes">
                <attribute name="attrs">{'invisible': [('payment_method_code', '=', 'delivered_third_check')]}
                </attribute>
            </label>
            <div name="amount_div" position="attributes">
                <attribute name="attrs">{'invisible': [('payment_method_code', '=', 'delivered_third_check')]}
                </attribute>
            </div>
            <xpath expr="//field[@name='journal_id']" position="after">
                <field name="treasury_journal_id" domain="[('type', '=', 'cash')]"
                       attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'delivered_third_check'])]}"/>
            </xpath>
        </field>
    </record>

    <!--  Checks search view -->
    <record id="view_account_payment_search" model="ir.ui.view">
        <field name="name">account.payment.check.search</field>
        <field name="model">account.payment</field>
        <field name="inherit_id" ref="account.view_account_payment_search"/>
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="check_ids"/>
            </field>
        </field>
    </record>

    <record id="view_account_payment_register_inherit" model="ir.ui.view">
        <field name="name">account.payment.check.register.inherit</field>
        <field name="model">account.payment.register</field>
        <field name="inherit_id" ref="account.view_account_payment_register_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@name='group1']" position="inside">

                <field name="payment_method_code" invisible='1'/>

                <field name="check_number"
                        attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check'])], 'required': [('payment_method_code', 'in', ['received_third_check', 'received_safty_check'])]}"/>
                <field name="check_name"
                        attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])], 'required': [('payment_method_code', 'in', ['received_third_check', 'received_safty_check'])]}"/>
                <field name="check_payment_date"
                        attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check', 'issue_check'])]}"/>
                <field name="check_bank_id"
                        attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check'])],  'required': [('payment_method_code', 'in', ['received_third_check', 'received_safty_check'])]}"/>
                <field name="check_owner_name"
                        attrs="{'invisible': [('payment_method_code', 'not in', ['received_third_check', 'received_safty_check'])],  'required': [('payment_method_code', 'in', ['received_third_check', 'received_safty_check'])]}"/>
                    
            </xpath>
        </field>
    </record>
</odoo>
