<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_invoice_form_inherit" model="ir.ui.view">
        <field name="name">account.move.form.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_invoice_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='payment_state']" position="after">
                <field name="ro_reconciliation_date"/>
            </xpath>
        </field>
    </record>

    <record id="view_account_invoice_filter_inherit" model="ir.ui.view">
        <field name="name">account.move.filter.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_account_invoice_filter"/>
        <field name="arch" type="xml">

            <xpath expr="//filter[@name='invoice_date']" position="after">
                <filter string="Reconciliation Date" name="ro_reconciliation_date" date="ro_reconciliation_date"/>
            </xpath>

            <xpath expr="//group/filter[@name='invoicedate']" position="before">
                <filter string="Reconciliation Date" name="reconciliation_date" context="{'group_by':'ro_reconciliation_date'}"/>
            </xpath>


        </field>
    </record>

</odoo>
