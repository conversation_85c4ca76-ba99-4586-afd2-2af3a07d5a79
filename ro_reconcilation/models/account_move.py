from odoo import models, fields, api


class AccountInvoice(models.Model):
    _inherit = 'account.move'

    ro_reconciliation_date = fields.Date(string='Reconciliation Date', compute='_compute_reconciliation_date', store=True)

    @api.depends('message_ids','amount_residual', 'move_type', 'state', 'company_id','payment_state')
    def _compute_reconciliation_date(self):
        for invoice in self:
            
            invoice.ro_reconciliation_date = False
            if invoice.state == 'posted' and invoice.payment_state == 'paid':
                if invoice.message_ids.tracking_value_ids.filtered(lambda val: val.field_desc in ['Payment Status','حالة الدفع'] and val.new_value_char in ['Paid','مدفوع']):
                    invoice.ro_reconciliation_date = invoice.message_ids.tracking_value_ids.filtered(lambda val: val.field_desc in ['Payment Status','حالة الدفع'] and val.new_value_char in ['Paid','مدفوع'])[:1].create_date.date()
            
            