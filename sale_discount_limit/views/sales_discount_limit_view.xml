<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <menuitem id="menu_sales_discount_limit_parent"
        name="Sales Discount Limit"
        parent="sale.menu_sale_config"
        groups="base.group_system"
    />

    <record id="sales_discount_limit_view_tree" model="ir.ui.view">
        <field name="name">sales.discount.limit.list</field>
        <field name="model">sales.discount.limit</field>
        <field name="arch" type="xml">
            <list string="Sales Discount Limit" editable="bottom">
                <field name="discount"/>
                <field name="user_ids" required="1" widget="many2many_tags" options="{'no_create': True, 'no_edit': True}"/>
                <!-- <field name="group_id" required="1" options="{'no_create': True, 'no_edit': True}"/> -->
            </list>
        </field>
    </record>

    <record id="sales_discount_limit_action" model="ir.actions.act_window">
        <field name="name">Sales Discount Limit</field>
        <field name="res_model">sales.discount.limit</field>
        <field name="view_mode">list</field>
    </record>

    <menuitem id="menu_sales_discount_limit"
        name="Sales Discount Limit"
        parent="sale_discount_limit.menu_sales_discount_limit_parent"
        action="sales_discount_limit_action"
        sequence="1"
        groups="base.group_system"
    />



</odoo>
