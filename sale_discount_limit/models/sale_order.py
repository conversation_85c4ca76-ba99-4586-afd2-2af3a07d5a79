# -*- coding: utf-8 -*-

from odoo import api, models, _
from odoo.exceptions import UserError


class SaleOrderLine(models.Model):
    """Check Discount amount."""

    _inherit = "sale.order.line"

    @api.constrains('discount')
    def _check_discount(self):

        for line in self:
            if line.discount:
                discount_amt = False
                for group in self.env['sales.discount.limit'].search([]):
                    if self.env.user in group.user_ids:
                        if discount_amt and group.discount < discount_amt:
                            continue
                        else:
                            discount_amt = group.discount


                        # break

                if discount_amt and line.discount > discount_amt and not self.env.user.has_group('base.group_system')\
                    and not self.env.user.has_group('sale_discount_limit.group_manager_disocunt_pers'):
                    raise UserError(
                        _('You are only eligible to give discounts up to'
                        ' %s .' % discount_amt))
