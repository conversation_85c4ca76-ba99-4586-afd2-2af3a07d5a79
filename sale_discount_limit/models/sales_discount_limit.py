# -*- coding: utf-8 -*-

from odoo import fields, models
import odoo.addons.decimal_precision as dp

class SalesDiscountLimit(models.Model):
    """Configuration for Set Sale Discount Limit."""

    _name = "sales.discount.limit"
    _description = "Configuration for Set Sale Discount Limit."
    # _rec_name = "group_id"
    # _sql_constraints = [
    #     ('group_id_uniq', 'unique(group_id)',
    #         'Group already exists!'),
    # ]

    # group_id = fields.Many2one(
    #     'res.groups', "Group", domain=lambda self: [
    #         ('category_id.id', '=',
    #             self.env.ref('base.module_category_sales_sales').id)])
    user_ids = fields.Many2many('res.users', string="Allowed users")
    discount = fields.Float("Discount (%)",
                            digits=dp.get_precision('Discount'), default=10.0)

    
