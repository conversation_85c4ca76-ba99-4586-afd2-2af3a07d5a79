<odoo>
    <data>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.account.discount</field>
            <field name="model">res.config.settings</field>
            <field name="priority" eval="50"/>
            <field name="inherit_id" ref="base.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='has_accounting_entries']" position="after">

                    <h2 attrs="{'invisible': [('has_chart_of_accounts','==',False)]}">Discount Account</h2>
                    <div class="row mt16 o_settings_container"
                         attrs="{'invisible': [('has_chart_of_accounts','==',False)]}">
                        <div class="col-xs-12 col-md-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="enable_discount_account"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for ='enable_discount_account'/>
                                <div class="text-muted">
                                    Activate discount account on invoice journal.
                                </div>

                            </div>
                        </div>
                        <div class="col-xs-12 col-md-6 o_setting_box" attrs="{'invisible':[('enable_discount_account','==',False)]}">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <span class="o_form_label">Accounts</span>
                                <div class="text-muted">
                                        Set default discount account.
                                </div>
                                <div class="content-group">
                                    <div class="row mt16" >
                                        <label for="discount_account"
                                               class="col-md-3 o_light_label"/>
                                        <field name="discount_account"
                                               attrs="{'required': [('enable_discount_account','==',True)]}"/>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                </xpath>
            </field>
        </record>


    </data>
</odoo>