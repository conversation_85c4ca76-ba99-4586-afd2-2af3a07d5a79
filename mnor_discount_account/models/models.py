# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class AccountMoveLine(models.Model):    
    _inherit = 'account.move.line'

    display_type = fields.Selection(selection_add=[('line_discount', 'Discount')],ondelete={'line_discount': 'cascade'})

class AccountMove(models.Model):

    _inherit = 'account.move'
    
    @api.onchange('invoice_line_ids')
    def _recompute_discount_lines(self, result=False):
        """This Function Create The General Entries for Discount"""

        result = result or self
        if self.env.company.enable_discount_account and self.env.company.discount_account:
            for rec in result:

                #remove all discount lines
                already_exists = rec.line_ids.filtered(
                            lambda line_move: line_move.name and line_move.name.find('Discount') == 0)
                if already_exists:
                    rec.with_context(check_move_validity=False).line_ids -= already_exists
                    terms_lines = rec.line_ids.filtered(
                        lambda line_move: line_move.account_id.account_type in ('asset_receivable', 'liability_payable'))
                        
                    other_lines = rec.line_ids.filtered(
                        lambda line_move: line_move.account_id.account_type not in ('asset_receivable', 'liability_payable'))
                    total_balance = sum(other_lines.mapped('balance'))
                    total_amount_currency = sum(other_lines.mapped('amount_currency'))
                    
                    terms_lines.update({
                        'amount_currency': -total_amount_currency,
                        'debit': total_balance < 0.0 and -total_balance or 0.0,
                        'credit': total_balance > 0.0 and total_balance or 0.0,
                    })

                type_list = ['out_invoice', 'out_refund', 'in_invoice', 'in_refund']

                for line in rec.line_ids:
                    if line.discount > 0 and rec.move_type in type_list:
                        if rec.is_invoice(include_receipts=True):
                            in_draft_mode = rec != rec._origin
                            discount_name = "Discount"

                            discount_value = " @" + str(line.discount) + "%"

                            discount_name = discount_name + discount_value

                            terms_lines = rec.line_ids.filtered(
                                lambda line_move: line_move.account_id.account_type in ('asset_receivable', 'liability_payable'))
                            
                            create_method = in_draft_mode and \
                                            self.env['account.move.line'].with_context(check_move_validity=False).new or \
                                            self.env['account.move.line'].with_context(check_move_validity=False).create

                            if self.env.company.discount_account \
                                    and (rec.move_type == "out_invoice"
                                        or rec.move_type == "out_refund"):
                                amount = (line.discount / 100) * (line.quantity*line.price_unit)
                                #discount line dict
                                dict = {
                                    'display_type':'line_discount',
                                    'move_name': rec.name,
                                    'name': discount_name,
                                    'price_unit': amount,
                                    'quantity': 1,
                                    'debit': amount < 0.0 and -amount or 0.0,
                                    'credit': amount > 0.0 and amount or 0.0,
                                    'account_id': self.env.company.discount_account.id,
                                    'move_id': rec._origin.id,
                                    'date': rec.date,
                                    # 'exclude_from_invoice_tab': True,
                                    'partner_id': terms_lines.partner_id.id,
                                    'company_id': terms_lines.company_id.id,
                                    'company_currency_id': terms_lines.company_currency_id.id,
                                    'analytic_distribution': line.analytic_distribution,
                                    # 'analytic_tag_ids': line.analytic_tag_ids.ids,
                                }

                                #product line inverse line
                                dict_line = {
                                    'display_type':'line_discount',
                                    'move_name': rec.name,
                                    'name': discount_name + ' on ' +line.name,
                                    'price_unit': amount,
                                    'quantity': 1,
                                    'debit': amount > 0.0 and amount or 0.0,
                                    'credit': amount < 0.0 and -amount or 0.0,
                                    'account_id': line.account_id.id,
                                    'move_id': rec._origin.id,
                                    'date': rec.date,
                                    # 'exclude_from_invoice_tab': True,
                                    'partner_id': terms_lines.partner_id.id,
                                    'company_id': terms_lines.company_id.id,
                                    'company_currency_id': terms_lines.company_currency_id.id,
                                    'analytic_distribution': line.analytic_distribution,
                                    # 'analytic_tag_ids': line.analytic_tag_ids.ids,
                                }
                                if rec.move_type == "out_invoice":
                                    dict.update({
                                        'debit': amount > 0.0 and amount or 0.0,
                                        'credit': amount < 0.0 and -amount or 0.0,
                                    })
                                    dict_line.update({
                                        'debit': amount < 0.0 and -amount or 0.0,
                                        'credit': amount > 0.0 and amount or 0.0,
                                    })
                                else:
                                    dict.update({
                                        'debit': amount < 0.0 and -amount or 0.0,
                                        'credit': amount > 0.0 and amount or 0.0,
                                    })
                                    dict_line.update({
                                        'debit': amount > 0.0 and amount or 0.0,
                                        'credit': amount < 0.0 and -amount or 0.0,
                                    })

                                rec.with_context(check_move_validity=False).line_ids += create_method(dict) + create_method(dict_line)
                
                terms_lines = rec.line_ids.filtered(
                    lambda line_move: line_move.account_id.account_type in ('asset_receivable', 'liability_payable'))
                    
                other_lines = rec.line_ids.filtered(
                    lambda line_move: line_move.account_id.account_type not in ('asset_receivable', 'liability_payable'))
                total_balance = sum(other_lines.mapped('balance'))
                total_amount_currency = sum(other_lines.mapped('amount_currency'))
                
                terms_lines.update({
                    'amount_currency': -total_amount_currency,
                    'debit': total_balance < 0.0 and -total_balance or 0.0,
                    'credit': total_balance > 0.0 and total_balance or 0.0,
                })
                 

class SaleOrder(models.Model):

    _inherit = 'sale.order'

    def _create_invoices(self, grouped=False, final=False, date=None):
        
        move = super(SaleOrder, self)._create_invoices(grouped, final, date)
        
        if self.env.company.enable_discount_account and self.env.company.discount_account:
            in_draft_mode = move != move._origin

            for line in move.invoice_line_ids:
                if line.discount > 0:
                    discount_value = " @" + str(line.discount) + "%"
                    discount_name = "Discount"

                    discount_name = discount_name + discount_value
                    create_method = in_draft_mode and \
                                    self.env['account.move.line'].with_context(check_move_validity=False).new or \
                                    self.env['account.move.line'].with_context(check_move_validity=False).create

                    terms_lines = move.line_ids.filtered(
                                        lambda line_move: line_move.account_id.account_type in ('asset_receivable', 'liability_payable'))

                    amount = (line.discount / 100) * (line.quantity*line.price_unit)
                    #discount line dict
                    dict = {
                        'display_type':'line_discount',
                        'move_name': move.name,
                        'name': discount_name + ' on ' +line.name,
                        'price_unit': amount,
                        'quantity': 1,
                        'debit': amount > 0.0 and amount or 0.0,
                        'credit': amount < 0.0 and -amount or 0.0,
                        'account_id': move.company_id.discount_account.id,
                        'move_id': move._origin.id,
                        'date': move.date,
                        # 'exclude_from_invoice_tab': True,
                        'partner_id': terms_lines.partner_id.id,
                        'company_id': terms_lines.company_id.id,
                        'company_currency_id': terms_lines.company_currency_id.id,
                        'analytic_distribution': line.analytic_distribution,
                        # 'analytic_tag_ids': line.analytic_tag_ids.ids,
                    }

                    #product line inverse line
                    dict_line = {
                        'display_type':'line_discount',
                        'move_name': move.name,
                        'name': discount_name + ' on ' +line.name,
                        'price_unit': amount,
                        'quantity': 1,
                        'debit': amount < 0.0 and -amount or 0.0,
                        'credit': amount > 0.0 and amount or 0.0,
                        'account_id': line.account_id.id,
                        'move_id': move._origin.id,
                        'date': move.date,
                        # 'exclude_from_invoice_tab': True,
                        'partner_id': terms_lines.partner_id.id,
                        'company_id': terms_lines.company_id.id,
                        'company_currency_id': terms_lines.company_currency_id.id,
                        'analytic_distribution': line.analytic_distribution,
                        # 'analytic_tag_ids': line.analytic_tag_ids.ids,
                    }
                    move.with_context(check_move_validity=False).line_ids += create_method(dict) + create_method(dict_line)

            # terms_lines = move.line_ids.filtered(
            #     lambda line_move: line_move.account_id.account_type in ('asset_receivable', 'liability_payable'))
            # other_lines = move.invoice_line_ids.filtered(
            #     lambda line_move: line_move.account_id.account_type not in ('asset_receivable', 'liability_payable'))


            # total_balance = sum(other_lines.mapped('balance'))
            # total_amount_currency = sum(other_lines.mapped('amount_currency'))

            # for record in terms_lines:
            #     record.update({
            #         'amount_currency': -total_amount_currency,
            #         'debit': -record.price_total if total_balance < 0.0 else 0.0,
            #         'credit': record.price_total if total_balance > 0.0 else 0.0
            #     })
        
        return move
        
