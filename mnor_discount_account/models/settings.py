from odoo import models, fields, api


class ResCompany(models.Model):
    _inherit = "res.company"

    enable_discount_account = fields.<PERSON><PERSON>an(string="Activate Discount Account", groups="base.group_user")
    discount_account = fields.Many2one('account.account', string="Discount Account", groups="base.group_user")

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    enable_discount_account = fields.Boolean(related='company_id.enable_discount_account', string="Activate Discount Account", groups="base.group_user", readonly=False)
    discount_account = fields.Many2one(comodel_name= 'account.account', related='company_id.discount_account',string="Discount Account", groups="base.group_user", readonly=False)
