<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>

        <template id="plan_analytic_account_report_html">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <table class="table table-sm o_main_table table-borderless mt-4">
                        <thead style="display: table-row-group">
                            <tr>
                                <t t-foreach="plans" t-as="plan">
                                    <th class="text-start">
                                        <span t-esc="plan" />
                                    </th>
                                </t>
                                <th class="text-start">Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="rows" t-as="row">
                                <tr>
                                    <t t-foreach="rows[row]" t-as="row_line">

                                        <td>
                                            <span t-esc="row_line" />
                                        </td>

                                    </t>
                                </tr>
                            </t>
                        </tbody>

                    </table>
                </t>
            </t>
        </template>

        <record id="plan_analytic_account_report" model="ir.actions.report">
            <field name="name">Plan Analytic Account</field>
            <field name="model">print.analytic</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_print_plan_analytic_account.plan_analytic_account_report_html</field>
            <field name="report_file">ro_print_plan_analytic_account.plan_analytic_account_report_html</field>
            <field name="print_report_name">'Plan Analytic Account Report'</field>
            <field name="binding_type">report</field>
        </record>
    </data>
</odoo>