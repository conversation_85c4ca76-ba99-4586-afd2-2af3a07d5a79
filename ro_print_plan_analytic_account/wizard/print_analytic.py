# -*- coding: utf-8 -*-

from odoo import _, fields, models


class PrintAnalytic(models.TransientModel):
    _name = 'print.analytic'
    _description = 'Print Analytic'

    ro_print_analytic_line_ids = fields.One2many(
        'print.analytic.line', 'ro_print_analytic_id', string='Print Analytic Lines')

    def print_pdf(self):


        total_analytic_dict = total_analytic_dict_tmp = []

        for line in self.ro_print_analytic_line_ids:

            total_analytic_dict_tmp = total_analytic_dict
            total_analytic_dict = []

            if not total_analytic_dict_tmp:
                for analytic_account in line.ro_analytic_account_ids:
                    total_analytic_dict.append(['{}'.format(analytic_account.id)])

            for  analytic_dict in total_analytic_dict_tmp:
                for analytic_account in line.ro_analytic_account_ids:
                    total_analytic_dict.append(analytic_dict + ['{}'.format(analytic_account.id)])

        total_res = {}
        
        for idx, analytic_list in enumerate(total_analytic_dict):
            query = """
            select coalesce(sum(aml.price_subtotal),0) from account_move_line aml
            left join account_move am on am.id = aml.move_id
            where aml.analytic_distribution ?& %(ids)s and am.state = 'posted'
            """
            self.env.cr.execute(query, {'ids': analytic_list})

            res = self.env.cr.fetchall()[0]
            

            analytic_list_names = self.ro_print_analytic_line_ids.ro_analytic_account_ids.filtered(lambda analytic: analytic.id in [int(id) for id in analytic_list]).mapped('name')

            if res[0] > 0:
                total_res[str(idx)] = analytic_list_names + [res[0]]

        data = {
            'plans': self.ro_print_analytic_line_ids.ro_analytic_plan_id.mapped('name'),
            'rows': total_res
        }

        return self.env.ref('ro_print_plan_analytic_account.plan_analytic_account_report').report_action(self, data=data)

class PrintAnalyticLine(models.TransientModel):
    _name = 'print.analytic.line'
    _description = 'Print Analytic Line'

    ro_print_analytic_id = fields.Many2one('print.analytic')

    ro_analytic_plan_id = fields.Many2one(
        'account.analytic.plan', string='Analytic Plan', required=True)
    
    ro_analytic_account_ids = fields.Many2many(
        'account.analytic.account', string='Analytic Accounts', 
        domain="[('plan_id','=',ro_analytic_plan_id)]", required=True
        )
