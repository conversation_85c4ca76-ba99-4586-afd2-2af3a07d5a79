# -*- coding: utf-8 -*-
import logging

from odoo import models, fields, api, _
from odoo.exceptions import AccessError, UserError, ValidationError
_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    allow_confirm = fields.Boolean(string='Allow Confirm', default=False)


    def action_confirm(self):
        if self.allow_confirm == True:
            return super(SaleOrder, self).action_confirm()
        else:
            continue_confirm = True
            message =  ''
            for line in self.order_line:
                bill_of_materials = line.product_id.variant_bom_ids or line.product_id.bom_ids
                if bill_of_materials:
                    for bom in bill_of_materials.bom_line_ids:
                        bill_of_materials_second_level = bom.product_id.variant_bom_ids or bom.product_id.bom_ids
                        # if there is second level so need to check second level else check only first level
                        if bill_of_materials_second_level:

                            for bom2 in bill_of_materials_second_level.bom_line_ids:
                                if bom2.product_id.with_context({'warehouse' : self.warehouse_id.id}).free_qty < bom2.product_qty :
                                # if bom2.product_id.qty_available <= 0:
                                    continue_confirm = False
                                    message += 'Product'+line.product_id.name + '\n'

                                    message = message + ' wating quantity from product'+ bom2.product_id.display_name + ' please check on hand of product \n'
                        else:
                            # for bom in bom.bom_line_ids:
                            if bom.product_id.with_context({'warehouse' : self.warehouse_id.id}).free_qty < bom.product_qty:
                                continue_confirm = False
                                message += 'Product'+line.product_id.name + '\n'
                                message = message + ' wating quantity from product'+ bom.product_id.display_name + ' please check on hand of product \n'
            if continue_confirm == True:
                return super(SaleOrder, self).action_confirm()
            else:
                raise ValidationError(_(message))


                
