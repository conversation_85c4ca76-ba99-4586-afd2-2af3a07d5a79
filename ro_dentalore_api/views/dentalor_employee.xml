<?xml version='1.0' encoding='utf-8'?>
<odoo>
<!-- <record id="dentalor_employees_server_action" model="ir.actions.server">
    <field name="name">Dentalor Employees</field>
    <field name="model_id" ref="ro_dentalore_api.model_dentalor_employees"/>
    <field name="binding_model_id" ref="ro_dentalore_api.model_dentalor_employees"/>
    <field name="state">code</field>
    <field name="code">action = model.get_dentalor_employees()</field>
</record> -->
  <record id="dentalor_employees_tree_view" model="ir.ui.view">
    <field name="name">dentalor.employees.view.tree</field>
    <field name="model">dentalor.employees</field>
    <field name="arch" type="xml">
      <tree string="Employees">
        <header>
            <button name="get_dentalor_employees" type="object" string="Check Employees"/>
        </header>
        <field name="name" />
        <field name="userid"/>
      </tree>
    </field>
  </record>

  <record id="dentalor_employees_form_view" model="ir.ui.view">
    <field name="name">dentalor.employees.view.form</field>
    <field name="model">dentalor.employees</field>
    <field name="arch" type="xml">
      <form string="Employee">
        <sheet>
          <div class="oe_title">
            <label for="name" string="Employee Name"/>
            <h1>
                <div class="d-flex">
                    <field class="o_text_overflow" name="name"/>
                </div>
            </h1>
          </div>
          <group>
          <field name="userid"/>
          
          </group>
        </sheet>
      </form>
    </field>
  </record>


  <record id="dentalor_employees_view_search" model="ir.ui.view">
    <field name="name">dentalor.employees.view.search</field>
    <field name="model">dentalor.employees</field>
    <field name="arch" type="xml">
      <search string="Employees">
        <field name="name" string="Employee" filter_domain="[('name', 'ilike', self)]"/>
      </search>
    </field>
  </record>


  
  <record id="action_dentalor_employees_act_window" model="ir.actions.act_window">
      <field name="name">Dentalor Employees</field>
      <field name="res_model">dentalor.employees</field>
      <field name="view_mode">tree,form</field>
      <field name="target">current</field>
      <field name="view_id" ref="ro_dentalore_api.dentalor_employees_tree_view"/>
      <field name="search_view_id" ref="ro_dentalore_api.dentalor_employees_view_search"/>
  </record>

  <menuitem id="menu_action_dentalor_employees_act_window" action="ro_dentalore_api.action_dentalor_employees_act_window" sequence="116" parent="contacts.res_partner_menu_config"/>
  <!-- <menuitem id="menu_dentalor_employees_server_action" action="ro_dentalore_api.dentalor_employees_server_action" sequence="117" parent="contacts.res_partner_menu_config"/> -->

  
</odoo>