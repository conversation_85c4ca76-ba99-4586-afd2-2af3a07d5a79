<?xml version='1.0' encoding='utf-8'?>
<odoo>
  <record id="add_dentalore_field_in_contact_form_view" model="ir.ui.view">
    <field name="model">res.partner</field>
    <field name="inherit_id" ref="base.view_partner_form"/>
    <field name="arch" type="xml">

      <xpath expr="//sheet" position="before">
        <header>
          <button name="send_data_to_dentalore" type="object" string="Send Info To Dentalore" data-hotkey="w"/>
          <button name="update_data_in_dentalore" type="object" string="Update Info In Dentalore" data-hotkey="w"/>
        </header>
      </xpath>
      <!-- <xpath expr="//field[@name='vat']" position="after"> -->
       
        <!-- <field name="is_customer" widget="boolean_toggle"/>
        <field name="ssn_no" />
        <field name="referral_id" />
        <field name="branch_id" /> -->
      <!-- </xpath> -->

      <xpath expr="//notebook" position="inside">
        <page string="Dentalore Data" name="dentalore_data">
          <group>
            <group >
              <field name="is_customer" widget="boolean_toggle"/>
              <field name="ssn_no" />
              <field name="referral_id" />
              <field name="branch_id" />
            </group>
            <group>
              <field name="related_employee_id" />
              <field name="related_insurance_id" />
              <field name="related_patient_id" domain="[('patient_id', '!=', False)]"/>
            </group>
            
          </group>
          <group>
            <field name="referraltext" />
          </group>
        </page>


        <page string="Dentalore Api" name="dentalore_api">
          <field name="sent_to_dentalore" readonly="1"/>
          <field name="need_update_to_dentalore" readonly="1"/>
          <field name="dentalore_massage" readonly="1"/>
        </page>

      </xpath>
    </field>
  </record>


  <!-- add filters for dentalore api -->
  <record id="add_dentalore_filters_in_contact_search_view" model="ir.ui.view">
    <field name="model">res.partner</field>
    <field name="inherit_id" ref="base.view_res_partner_filter"/>
    <field name="arch" type="xml">
      <xpath expr="//filter[@name='type_company']" position="after">
        <filter string="Sent to Dentalore" name="sent_to_dentalore" domain="[('sent_to_dentalore', '=', True), ('is_customer', '=', True)]"/>
        <filter string="Not Sent to Dentalore" name="not_sent_to_dentalore" domain="[('sent_to_dentalore', '=', False), ('is_customer', '=', True)]"/>

        
      </xpath>
      
    </field>
  </record>
</odoo>