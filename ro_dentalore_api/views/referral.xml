<?xml version='1.0' encoding='utf-8'?>
<odoo>
<record id="clinic_referral_server_action" model="ir.actions.server">
    <field name="name">Clinic Referral</field>
    <!-- <field name="name">Update Clinic referral</field> -->
    <field name="model_id" ref="ro_dentalore_api.model_clinic_referral"/>
    <field name="binding_model_id" ref="ro_dentalore_api.model_clinic_referral"/>
    <field name="state">code</field>
    <field name="code">action = model.get_referral()</field>
</record>
  <record id="clinic_referral_tree_view" model="ir.ui.view">
    <field name="name">clinic.referral.view.tree</field>
    <field name="model">clinic.referral</field>
    <field name="arch" type="xml">
      <tree string="Referral">
        <header>
            <button name="get_referral" type="object" string="Check Referral"/>
        </header>
        <field name="name" />
        <field name="value"/>
      </tree>
    </field>
  </record>

  <record id="clinic_referral_form_view" model="ir.ui.view">
    <field name="name">clinic.referral.view.form</field>
    <field name="model">clinic.referral</field>
    <field name="arch" type="xml">
      <form string="Referral">
        <sheet>
          <div class="oe_title">
            <label for="name" string="Referral Name"/>
            <h1>
                <div class="d-flex">
                    <field class="o_text_overflow" name="name"/>
                </div>
            </h1>
          </div>
          <group>
          <field name="value"/>
          
          </group>
        </sheet>
      </form>
    </field>
  </record>


  <record id="clinic_referral_view_search" model="ir.ui.view">
    <field name="name">clinic.referral.view.search</field>
    <field name="model">clinic.referral</field>
    <field name="arch" type="xml">
      <search string="referral">
        <field name="name" string="referral" filter_domain="[('name', 'ilike', self)]"/>
      </search>
    </field>
  </record>


  
  <record id="action_clinic_referral_act_window" model="ir.actions.act_window">
      <field name="name">Clinic Referral</field>
      <field name="res_model">clinic.referral</field>
      <field name="view_mode">tree,form</field>
      <field name="target">current</field>
      <field name="view_id" ref="ro_dentalore_api.clinic_referral_tree_view"/>
      <field name="search_view_id" ref="ro_dentalore_api.clinic_referral_view_search"/>
  </record>

  <!-- <menuitem id="menu_action_clinic_referral_act_window" action="ro_dentalore_api.action_clinic_referral_act_window" sequence="116" parent="contacts.res_partner_menu_config"/> -->
  <menuitem id="menu_clinic_referral_server_action" action="ro_dentalore_api.clinic_referral_server_action" sequence="117" parent="contacts.res_partner_menu_config"/>

  
</odoo>