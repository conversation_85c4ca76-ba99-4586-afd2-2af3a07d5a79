<?xml version='1.0' encoding='utf-8'?>
<odoo>
<!-- <record id="insurance_server_action" model="ir.actions.server">
    <field name="name">Insurance</field>
    <field name="model_id" ref="ro_dentalore_api.model_insurance"/>
    <field name="binding_model_id" ref="ro_dentalore_api.model_insurance"/>
    <field name="state">code</field>
    <field name="code">action = model.get_insurance()</field>
</record> -->
  <record id="insurance_tree_view" model="ir.ui.view">
    <field name="name">insurance.view.tree</field>
    <field name="model">insurance</field>
    <field name="arch" type="xml">
      <tree string="insurance">
        <header>
            <button name="get_insurance" type="object" string="Check insurance"/>
        </header>
        <field name="name" />
        <field name="code"/>
        <field name="insuranceid"/>
      </tree>
    </field>
  </record>

  <record id="insurance_form_view" model="ir.ui.view">
    <field name="name">insurance.view.form</field>
    <field name="model">insurance</field>
    <field name="arch" type="xml">
      <form string="insurance">
        <sheet>
          <div class="oe_title">
            <label for="name" string="insurance Name"/>
            <h1>
                <div class="d-flex">
                    <field class="o_text_overflow" name="name"/>
                </div>
            </h1>
          </div>
          <group>
            <field name="code"/>
            <field name="insuranceid"/>
          
          </group>
        </sheet>
      </form>
    </field>
  </record>


  <record id="insurance_view_search" model="ir.ui.view">
    <field name="name">insurance.view.search</field>
    <field name="model">insurance</field>
    <field name="arch" type="xml">
      <search string="insurance">
        <field name="name" string="insurance" filter_domain="[('name', 'ilike', self)]"/>
      </search>
    </field>
  </record>


  
  <record id="action_insurance_act_window" model="ir.actions.act_window">
      <field name="name">Insurance</field>
      <field name="res_model">insurance</field>
      <field name="view_mode">tree,form</field>
      <field name="target">current</field>
      <field name="view_id" ref="ro_dentalore_api.insurance_tree_view"/>
      <field name="search_view_id" ref="ro_dentalore_api.insurance_view_search"/>
  </record>

  <menuitem id="menu_action_insurance_act_window" action="ro_dentalore_api.action_insurance_act_window" sequence="116" parent="contacts.res_partner_menu_config"/>
  <!-- <menuitem id="menu_insurance_server_action" action="ro_dentalore_api.insurance_server_action" sequence="117" parent="contacts.res_partner_menu_config"/> -->

  
</odoo>