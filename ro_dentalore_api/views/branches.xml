<?xml version='1.0' encoding='utf-8'?>
<odoo>
<record id="clinic_branches_server_action" model="ir.actions.server">
    <field name="name">Clinic Branches</field>
    <!-- <field name="name">Update Clinic Branches</field> -->
    <field name="model_id" ref="ro_dentalore_api.model_clinic_branches"/>
    <field name="binding_model_id" ref="ro_dentalore_api.model_clinic_branches"/>
    <field name="state">code</field>
    <field name="code">action = model.get_branches()</field>
</record>
  <record id="clinic_branches_tree_view" model="ir.ui.view">
    <field name="name">clinic.branches.view.tree</field>
    <field name="model">clinic.branches</field>
    <field name="arch" type="xml">
      <tree string="Branch">
        <header>
            <button name="get_branches" type="object" string="Check Branches"/>
        </header>
        <field name="name" />
        <field name="branch_id"/>
      </tree>
    </field>
  </record>

  <record id="clinic_branches_form_view" model="ir.ui.view">
    <field name="name">clinic.branches.view.form</field>
    <field name="model">clinic.branches</field>
    <field name="arch" type="xml">
      <form string="Branch">
        <sheet>
          <div class="oe_title">
            <label for="name" string="Branch Name"/>
            <h1>
                <div class="d-flex">
                    <field class="o_text_overflow" name="name"/>
                </div>
            </h1>
          </div>
          <group>
          <field name="branch_id"/>
          
          </group>
        </sheet>
      </form>
    </field>
  </record>


  <record id="clinic_branches_view_search" model="ir.ui.view">
    <field name="name">clinic.branches.view.search</field>
    <field name="model">clinic.branches</field>
    <field name="arch" type="xml">
      <search string="Branch">
        <field name="name" string="Branch" filter_domain="[('name', 'ilike', self)]"/>
      </search>
    </field>
  </record>


  
  <record id="action_clinic_branches_act_window" model="ir.actions.act_window">
      <field name="name">Clinic Branches</field>
      <field name="res_model">clinic.branches</field>
      <field name="view_mode">tree,form</field>
      <field name="target">current</field>
      <field name="view_id" ref="ro_dentalore_api.clinic_branches_tree_view"/>
      <field name="search_view_id" ref="ro_dentalore_api.clinic_branches_view_search"/>
  </record>

  <!-- <menuitem id="menu_action_clinic_branches_act_window" action="ro_dentalore_api.action_clinic_branches_act_window" sequence="116" parent="contacts.res_partner_menu_config"/> -->
  <menuitem id="menu_clinic_branches_server_action" action="ro_dentalore_api.clinic_branches_server_action" sequence="117" parent="contacts.res_partner_menu_config"/>

  
</odoo>