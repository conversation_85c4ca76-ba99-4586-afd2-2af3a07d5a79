<?xml version='1.0' encoding='utf-8'?>
<odoo>
<!-- <record id="dentalor_provider_server_action" model="ir.actions.server">
    <field name="name">Dentalor provider</field>
    <field name="model_id" ref="ro_dentalore_api.model_dentalor_provider"/>
    <field name="binding_model_id" ref="ro_dentalore_api.model_dentalor_provider"/>
    <field name="state">code</field>
    <field name="code">action = model.get_dentalor_provider()</field>
</record> -->
  <record id="dentalor_provider_tree_view" model="ir.ui.view">
    <field name="name">dentalor.provider.view.tree</field>
    <field name="model">dentalor.provider</field>
    <field name="arch" type="xml">
      <tree string="provider">
        <header>
            <button name="get_dentalor_provider" type="object" string="Check provider"/>
        </header>
        <field name="name" />
        <field name="providerid"/>
      </tree>
    </field>
  </record>

  <record id="dentalor_provider_form_view" model="ir.ui.view">
    <field name="name">dentalor.provider.view.form</field>
    <field name="model">dentalor.provider</field>
    <field name="arch" type="xml">
      <form string="provider">
        <sheet>
          <div class="oe_title">
            <label for="name" string="provider Name"/>
            <h1>
                <div class="d-flex">
                    <field class="o_text_overflow" name="name"/>
                </div>
            </h1>
          </div>
          <group>
          <field name="providerid"/>
          
          </group>
        </sheet>
      </form>
    </field>
  </record>


  <record id="dentalor_provider_view_search" model="ir.ui.view">
    <field name="name">dentalor.provider.view.search</field>
    <field name="model">dentalor.provider</field>
    <field name="arch" type="xml">
      <search string="provider">
        <field name="name" string="Provider" filter_domain="[('name', 'ilike', self)]"/>
      </search>
    </field>
  </record>


  
  <record id="action_dentalor_provider_act_window" model="ir.actions.act_window">
      <field name="name">Dentalor provider</field>
      <field name="res_model">dentalor.provider</field>
      <field name="view_mode">tree,form</field>
      <field name="target">current</field>
      <field name="view_id" ref="ro_dentalore_api.dentalor_provider_tree_view"/>
      <field name="search_view_id" ref="ro_dentalore_api.dentalor_provider_view_search"/>
  </record>

  <menuitem id="menu_action_dentalor_provider_act_window" action="ro_dentalore_api.action_dentalor_provider_act_window" sequence="116" parent="contacts.res_partner_menu_config"/>
  <!-- <menuitem id="menu_dentalor_provider_server_action" action="ro_dentalore_api.dentalor_provider_server_action" sequence="117" parent="contacts.res_partner_menu_config"/> -->

  
</odoo>