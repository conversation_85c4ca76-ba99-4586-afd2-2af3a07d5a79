<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="create_dentalore_data" model="ir.cron">
            <field name="name">Create Patient in Dentalore Api</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="code">model.create_patients_in_dentalore()</field>
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">5</field>
            <field name="priority" eval="1" />
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="nextcall" eval="(datetime.now(pytz.timezone('Africa/Cairo'))).strftime('%Y-%m-%d 03:00:00')"/>
            <field eval="True" name="doall" />
        </record>


        <record id="update_patient_in_dentalore_data" model="ir.cron">
            <field name="name">Update Patient in Dentalore Api</field>
            <field name="model_id" ref="base.model_res_partner"/>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="code">model.update_patients_in_dentalore()</field>
            <field name="user_id" ref="base.user_root" />
            <field name="interval_number">5</field>
            <field name="priority" eval="1" />
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="nextcall" eval="(datetime.now(pytz.timezone('Africa/Cairo'))).strftime('%Y-%m-%d 03:00:00')"/>
            <field eval="True" name="doall" />
        </record>

    </data>
</odoo>
