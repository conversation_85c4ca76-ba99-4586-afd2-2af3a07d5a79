# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import requests
import datetime
import json
import logging
_logger = logging.getLogger(__name__)


class DentalorEmployee(models.Model):
    _name = 'dentalor.employees'

    name = fields.Char(string='FullName')
    userid = fields.Char(string="UserId")

    def get_dentalor_employees(self):
        values = {
            "PageSize": 15,
            "PageIndex": int(self.env.company.employee_page_index + 1),
            "SearchText": ""
        }
        _logger.warning('values::::::::::::::::::')
        _logger.warning(values)
        headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}

        request = requests.post('https://clinic.nebras-dentalore.com/shinywhitelive/APIs/V2/User/GetEmployees', headers=headers, data=json.dumps(values))
        # request = requests.get('https://qa-clinic.nebras-dentalore.com/itidaclinic/wapi/PatientAPI/GetBranchList?clinicId=85D65D92-2DE6-44B6-9707-0D8F2FBBF3CD', headers=headers) # ==>test

        response_json = json.loads(request.text)

        if response_json:
            _logger.warning('response_json::::::::::::::::::')
            _logger.warning(response_json)
            for rec in response_json['Users']:
                check_employee = self.env['dentalor.employees'].search([('userid', '=', rec.get('UserId'))])
                if not check_employee:
                    self.env['dentalor.employees'].create({'name':rec.get('FullName'), 'userid':rec.get('UserId')})
                else:
                    check_employee.write({'name':rec.get('FullName'), 'userid':rec.get('UserId')})

            page_index = self.env.company.employee_page_index + 1 if self.env.company.employee_current_count + 15 < response_json['TotalCount'] else -1
            current_count = self.env.company.employee_current_count + 15 if self.env.company.employee_current_count + 15 < response_json['TotalCount'] else 0

            self.env.company.sudo().write({'employee_page_index': page_index, 'employee_current_count':current_count})

        



        # result = {
        #     "type": "ir.actions.act_window",
        #     "res_model": "dentalor.employees",
        #     "name": _("Dentalor Employees"),
        #     'view_mode': 'tree,form',
        # }
        # return result

