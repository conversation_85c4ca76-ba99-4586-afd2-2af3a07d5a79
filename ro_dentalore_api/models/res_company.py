# -*- coding: utf-8 -*-

import logging

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import requests
import datetime
import json
_logger = logging.getLogger(__name__)

class ResCompany(models.Model):
    _inherit = 'res.company'

    employee_page_index = fields.Float(default=-1)
    employee_current_count = fields.Float()

    insurance_page_index = fields.Float(default=-1)
    insurance_current_count = fields.Float()


    provider_page_index = fields.Float(default=-1)
    provider_current_count = fields.Float()