# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import requests
import datetime
import json

class ClinicReferral(models.Model):
    _name = 'clinic.referral'

    name = fields.Char()
    value = fields.Char(string="Value")

    def get_referral(self):
        headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}

        request = requests.get('https://clinic.nebras-dentalore.com/shinywhitelive//wapi/PatientAPI/GetReferralList?clinicId=04e3f603-4f15-4eb3-9c37-a12169cbd9a7', headers=headers)
        # request = requests.get('https://qa-clinic.nebras-dentalore.com/itidaclinic/wapi/PatientAPI/GetBranchList?clinicId=85D65D92-2DE6-44B6-9707-0D8F2FBBF3CD', headers=headers) # ==>test

        response_json = json.loads(request.text)

        if response_json:
            for rec in response_json:
                check_branch = self.env['clinic.referral'].search([('value', '=', rec.get('Value'))])
                if not check_branch:
                    self.env['clinic.referral'].create({'name':rec.get('Text'), 'value':rec.get('Value')})
                else:
                    check_branch.write({'name':rec.get('Text'), 'value':rec.get('Value')})



        result = {
            "type": "ir.actions.act_window",
            "res_model": "clinic.referral",
            "name": _("Clinic Referral"),
            'view_mode': 'tree,form',
        }
        return result

