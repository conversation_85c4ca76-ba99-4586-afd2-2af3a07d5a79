# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import requests
import datetime
import json

class ClinicBranches(models.Model):
    _name = 'clinic.branches'

    name = fields.Char()
    branch_id = fields.Char(string="Branch ID")

    def get_branches(self):
        headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}

        request = requests.get('https://clinic.nebras-dentalore.com/shinywhitelive/wapi/PatientAPI/GetBranchList?clinicId=04e3f603-4f15-4eb3-9c37-a12169cbd9a7', headers=headers)
        # request = requests.get('https://qa-clinic.nebras-dentalore.com/itidaclinic/wapi/PatientAPI/GetBranchList?clinicId=85D65D92-2DE6-44B6-9707-0D8F2FBBF3CD', headers=headers) # ==>test

        response_json = json.loads(request.text)
        print('response_json')
        print(response_json)
        print(type(response_json))
        [{'BranchId': '9e354901-e22c-42f1-909c-3a2ef01412d9', 'Name': 'Maddi', 'Address': {'Country': 'EG', 'Address1': 'Mansoura', 'Address2': None, 'City': None, 'State': 'Mansoura', 'ZipCode': None, 'BuildingNumber': None}, 'Phone': {'Phone1': '123456', 'Phone2': None, 'Mobile': None, 'Fax': None}, 'TimeZone': 'Africa/Cairo', 'CreatedOn': '2013-10-31T14:45:15', 'IsActive': True},
          {'BranchId': '80c61b78-1b83-4ea0-a2ca-c15cbf6ecf82', 'Name': 'Dokki', 'Address': {'Country': 'EG', 'Address1': '53 Amman St., El Dokki (From Midheal Bakhoum St.)', 'Address2': 'Cairo', 'City': None, 'State': 'Cairo', 'ZipCode': None, 'BuildingNumber': None}, 'Phone': {'Phone1': '123', 'Phone2': None, 'Mobile': None, 'Fax': None}, 'TimeZone': 'Africa/Cairo', 'CreatedOn': '2013-10-31T11:02:29', 'IsActive': True}]

        if response_json:
            for rec in response_json:
                print(rec)
                print(rec.get('BranchId'))
                check_branch = self.env['clinic.branches'].search([('branch_id', '=', rec.get('BranchId'))])
                if not check_branch:
                    self.env['clinic.branches'].create({'name':rec.get('Name'), 'branch_id':rec.get('BranchId')})
                else:
                    check_branch.write({'name':rec.get('Name'), 'branch_id':rec.get('BranchId')})



        result = {
            "type": "ir.actions.act_window",
            "res_model": "clinic.branches",
            "name": _("Clinic Branches"),
            'view_mode': 'tree,form',
        }
        return result

