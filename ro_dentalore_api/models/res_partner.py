# -*- coding: utf-8 -*-

import logging

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import requests
import datetime
import json
_logger = logging.getLogger(__name__)

class ResPartner(models.Model):
    _inherit = 'res.partner'

    # id2_no = fields.Char(string="ID2")
    ssn_no = fields.Char(string="SSN")
    referral_id = fields.Many2one('clinic.referral',string="Referred IN")
    # referred_in = fields.Char(string="Referred IN")
    is_customer = fields.Boolean()
    sent_to_dentalore = fields.Boolean(default=False, string="dentalore Sent Check")
    need_update_to_dentalore = fields.Boolean(default=False)
    dentalore_massage = fields.Char()
    branch_id = fields.Many2one('clinic.branches', string="Branch")
    related_employee_id = fields.Many2one('dentalor.employees', string="Employee")
    provider_id = fields.Many2one('dentalor.provicer', string="Provider")
    related_insurance_id = fields.Many2one('insurance', string="Insurance")
    related_patient_id= fields.Many2one('res.partner', string="Insurance")
    referraltext = fields.Char(string="ReferralText For Insurance")
    
    


    def prepare_dentalore_values(self, need_update=False):
        patient_split_name = self.name.split()
        first_name = patient_split_name[0]
        last_name = ''
        for rec in patient_split_name[1:]:
            last_name += rec + ' '

        values = {
            
            # "SSN":self.ssn_no if self.ssn_no else '',
            "FirstName":first_name,
            "MiddleName":last_name,
            # "LastName":' .',
            # "Title":self.title.name,
            # "Gender":"1" if self.gender == 'male' else "2",

            # "Occupation":self.function,
            "Notes": "odoo id : "+ str(self.id)+ "----"+ ("Dentalore API" if not self.comment else self.comment) ,
            # "Address":self.street,
            # "State":self.state_id.name,
            # "CountryCode":self.country_id.code,
            # "Country":self.country_id.name,
            # "Mobile":self.phone,
            # "Home":self.phone,
            # "Work":self.mobile,
            # "Fax":self.mobile,
            # "referredin":self.referred_in
        }
        if self.title:
            values["Title"] = self.title.name

        if self.gender:
            values["Gender"] = "1" if self.gender == 'male' else "2"

        if self.function:
            values["Occupation"] = self.function

        if self.street:
            values["Address"] = self.street

        if self.state_id:
            values["State"] = self.state_id.name


        if self.country_id:
            values["CountryCode"] = self.country_id.code
            values["Country"] = self.country_id.name
        
        if self.phone:
            values["Mobile"] = self.phone
            values["Home"] = self.phone
        

        if self.mobile:
            values["Work"] = self.mobile
            values["Fax"] = self.mobile
        
        if self.referral_id:
            values["referredin"] = self.referral_id.value

        if self.related_employee_id:
            values["ReferralEmployeeId"] = self.related_employee_id.userid
        
        if self.related_insurance_id:
            values["ReferralInsurancePlanId"] = self.related_insurance_id.insuranceid

        if self.related_patient_id:
            values["ReferralPatientId"] = self.related_patient_id.patient_id

        if self.referraltext:
            values["ReferralText"] = self.referraltext

        if self.provider_id:
            values["ReferralPhysicianId"] = self.provider_id.providerid


        

        if self.ssn_no:
            values["SSN"] = self.ssn_no

        if self.birth_date:
            values["BirthDate"] = self.birth_date.strftime('%Y-%m-%d')

        if self.email:
            values["Email"] = self.email

        if need_update:
            values["Id2"] = self.patient_id
            values["MatchById2"] = "true"
        
        else:
            if self.branch_id:
                values["BranchId"] = self.branch_id.branch_id
        _logger.error("values:::::::::::::::::::::")
        _logger.error(values)
        return values
    

    


    
    def write(self, vals):
        fields_need_in_dentalore = ["ssn_no","name","title","gender","function","birth_date", "email","comment"
                                    ,"street","state_id","country_id","phone","mobile","referral_id", "related_employee_id"
                                    ,"related_insurance_id","related_patient_id", "referraltext"]
        
        keysList = list(vals.keys())
        print(keysList)
        
        for key in keysList:
            if key in fields_need_in_dentalore:
                vals['need_update_to_dentalore'] = True
        result = super(ResPartner, self).write(vals)
    
        return result
    

    def send_data_to_dentalore(self):
        if not self.sent_to_dentalore and self.is_customer:
            values = self.prepare_dentalore_values(need_update=False)
            headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}
            # headers = {'Content-Type': 'application/json', 'ApiKey':'RzGyVFnbTt7y1MrX00LzyO4H3qpl39hdXHLiNK1wXk4='} # ==>test
            request = requests.post('https://clinic.nebras-dentalore.com/shinywhitelive/apis/V2/Patient/CreatePatient',
                                    data=json.dumps(values), headers=headers)

            response_json = json.loads(request.text)

            if 'Success' in response_json and response_json['Success'] == True:
                self.write({'sent_to_dentalore':True, 'dentalore_massage':response_json['Message'], 'patient_id':response_json['Id2']})
            else:
                self.write({'dentalore_massage':response_json})

    
    def update_data_in_dentalore(self):
        if self.need_update_to_dentalore and self.is_customer:
            values = self.prepare_dentalore_values(need_update=True)
            headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}
            # headers = {'Content-Type': 'application/json', 'ApiKey':'RzGyVFnbTt7y1MrX00LzyO4H3qpl39hdXHLiNK1wXk4='} # ==>test
            request = requests.post('https://clinic.nebras-dentalore.com/shinywhitelive/apis/V2/Patient/UpdatePatient',
                                    data=json.dumps(values), headers=headers)

            response_json = json.loads(request.text)

            if 'Success' in response_json and response_json['Success'] == True:
                self.write({'need_update_to_dentalore':False})
            else:
                self.write({'dentalore_massage':response_json})



    



    def create_patients_in_dentalore(self):
        partner_ids = self.env['res.partner'].sudo().search([('sent_to_dentalore', '=', False), ('is_customer','=',True),]).filtered(lambda x: not x.patient_id)
        # partner_ids = self.env['res.partner'].sudo().search([('sent_to_dentalore', '=', False), ('is_customer','=',True), ('ssn_no','!=','')]).filtered(lambda x: not x.patient_id)
        if partner_ids:
            for rec in partner_ids:
                values = rec.prepare_dentalore_values(need_update=False)

                # values = {
                #     # "Id2":rec.id,
                #     # "Id2":rec.id2_no,
                #     "SSN":rec.ssn_no,
                #     "FirstName":rec.name,
                #     "MiddleName":'.',
                #     "LastName":'.',
                #     "Title":rec.title.name,
                #     # "IsSelfHouseHead":"true",
                #     # "PatientStatus":"0",
                #     # "MaritalStatus":"0",
                #     # "Gender":"1", #==> value
                #     "Gender":"1" if rec.gender == 'male' else "2",
                #     "Occupation":rec.function,
                #     # "BirthDate":rec.birth_date.strftime('%Y-%m-%d'), #"1996-09-03", 
                #     # "Email":rec.email,
                #     "Notes": "odoo id : "+ str(rec.id)+ "----"+ ("Dentalore API" if not rec.comment else rec.comment) ,
                #     "Address":rec.street,
                #     "State":rec.state_id.name,
                #     "CountryCode":rec.country_id.code,
                #     "Country":rec.country_id.name,
                #     "Mobile":rec.phone,
                #     "Home":rec.phone,
                #     "Work":rec.mobile,
                #     "Fax":rec.mobile,
                #     "referredin":rec.referred_in
                # }
                # if rec.birth_date:
                #     values["BirthDate"] = rec.birth_date.strftime('%Y-%m-%d')

                # if rec.email:
                #     values["Email"] = rec.email
                
                # if rec.branch_id:
                #     values["BranchId"] = rec.branch_id.branch_id


                headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}
                request = requests.post('https://clinic.nebras-dentalore.com/shinywhitelive/apis/V2/Patient/CreatePatient',
                                        data=json.dumps(values), headers=headers)
                # headers = {'Content-Type': 'application/json', 'ApiKey':'RzGyVFnbTt7y1MrX00LzyO4H3qpl39hdXHLiNK1wXk4='}
                # request = requests.post('https://qa-clinic.nebras-dentalore.com/itidaclinic/apis/V2/Patient/CreatePatient',
                #                         data=json.dumps(values), headers=headers)

                response_json = json.loads(request.text)

                if 'Success' in response_json and response_json['Success'] == True:
                    rec.write({'sent_to_dentalore':True, 'dentalore_massage':response_json['Message'], 'patient_id':response_json['Id2']})
                else:
                    rec.write({'dentalore_massage':response_json})



    def update_patients_in_dentalore(self):
        partner_ids = self.env['res.partner'].sudo().search([('need_update_to_dentalore', '=', True), ('is_customer','=',True)])

        if partner_ids:
            for rec in partner_ids:
                values = rec.prepare_dentalore_values(need_update=True)


                # values = {
                #     "Id2":rec.patient_id,
                #     "MatchById2":"true",
                #     "SSN":rec.ssn_no,
                #     "FirstName":rec.name,
                #     "MiddleName":' ',
                #     "LastName":' ',
                #     "Title":rec.title.name,
                #     # "IsSelfHouseHead":"true",
                #     # "PatientStatus":"0",
                #     # "MaritalStatus":"0",
                #     "Gender":"1" if rec.gender == 'male' else "2",
                #     "Occupation":rec.function,
                #     # "BirthDate":rec.birth_date.strftime('%Y-%m-%d'), #"1996-09-03", 
                #     # "Email":rec.email,
                #     "Notes": "odoo id : "+ str(rec.id)+ "----"+ ("Dentalore API" if not rec.comment else rec.comment) ,
                #     "Address":rec.street,
                #     "State":rec.state_id.name,
                #     "CountryCode":rec.country_id.code,
                #     "Country":rec.country_id.name,
                #     "Mobile":rec.phone,
                #     "Home":rec.phone,
                #     "Work":rec.mobile,
                #     "Fax":rec.mobile,
                #     "referredin":rec.referred_in
                # }
                # if rec.birth_date:
                #     values["BirthDate"] = rec.birth_date.strftime('%Y-%m-%d')

                # if rec.email:
                #     values["Email"] = rec.email

                headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}
                request = requests.post('https://clinic.nebras-dentalore.com/shinywhitelive/apis/V2/Patient/UpdatePatient',
                                        data=json.dumps(values), headers=headers)

                response_json = json.loads(request.text)

                if 'Success' in response_json and response_json['Success'] == True:
                    rec.write({'need_update_to_dentalore':False})
                else:
                    rec.write({'dentalore_massage':response_json})





    # check unique fields patient_id and ssn


    @api.constrains('patient_id', 'ssn_no')
    def _check_unique_fields(self):
        for partner in self:
            domain = []

            if partner.patient_id:
                domain += [('patient_id', '=', partner.patient_id)]

            if partner.ssn_no:
                if domain:
                    domain.insert(0, '|')

                domain += [('ssn_no', '=', partner.ssn_no)]
            

            #>1 because current partner is counted
            if domain and self.search_count(domain) > 1:
                raise ValidationError(_("Patient ID and SSN NO. must be unique!"))
    




