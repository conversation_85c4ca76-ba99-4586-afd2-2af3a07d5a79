# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import requests
import datetime
import json

class Insurance(models.Model):
    _name = 'insurance'

    name = fields.Char(string='FullName')
    code = fields.Char(string='code')
    insuranceid = fields.Char(string="insurance id")

    def get_insurance(self):
        values = {
            "PageSize": 15,
            "PageIndex": int(self.env.company.insurance_page_index + 1),
            "SearchText": ""
        }
        headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}

        request = requests.post('https://clinic.nebras-dentalore.com/shinywhitelive/APIs/V2/Insurance/GetInsurancePlans', headers=headers, data=json.dumps(values))

        response_json = json.loads(request.text)

        if response_json:
            for rec in response_json['InsurancePlans']:
                check_insurance = self.env['insurance'].search([('insuranceid', '=', rec.get('Id'))])
                if not check_insurance:
                    self.env['insurance'].create({'name':rec.get('Name'), 'insuranceid':rec.get('Id'), 'code':rec.get('Code')})
                else:
                    check_insurance.write({'name':rec.get('Name'), 'insuranceid':rec.get('Id'), 'code':rec.get('Code')})
            
            page_index = self.env.company.insurance_page_index + 1 if self.env.company.insurance_current_count + 15 < response_json['TotalCount'] else -1
            current_count = self.env.company.insurance_current_count + 15 if self.env.company.insurance_current_count + 15 < response_json['TotalCount'] else 0

            self.env.company.sudo().write({'insurance_page_index': page_index, 'insurance_current_count':current_count})



        # result = {
        #     "type": "ir.actions.act_window",
        #     "res_model": "insurance",
        #     "name": _("Insurance"),
        #     'view_mode': 'tree,form',
        # }
        # return result

