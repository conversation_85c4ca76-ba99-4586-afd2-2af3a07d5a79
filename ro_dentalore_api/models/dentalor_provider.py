# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import requests
import datetime
import json

class DentalorEmployee(models.Model):
    _name = 'dentalor.provider'

    name = fields.Char(string='Provider Name')
    providerid = fields.Char(string="UserId")

    def get_dentalor_provider(self):
        values = {
            "PageSize": 15,
            "PageIndex": int(self.env.company.provider_page_index + 1),
            "SearchText": ""
        }
        headers = {'Content-Type': 'application/json', 'ApiKey':'dA0yuY9/5fC+t/dBpnRTV7cQghXtZQthFbyqidMhg4Y='}

        request = requests.post('https://clinic.nebras-dentalore.com/shinywhitelive/APIs/V2/Clinic/GetProviders', headers=headers, data=json.dumps(values))

        response_json = json.loads(request.text)

        if response_json:
            for rec in response_json['Providers']:
                check_provider = self.env['dentalor.provider'].search([('providerid', '=', rec.get('ProviderId'))])
                if not check_provider:
                    self.env['dentalor.provider'].create({'name':rec.get('ProviderName'), 'providerid':rec.get('ProviderId')})
                else:
                    check_provider.write({'name':rec.get('ProviderName'), 'providerid':rec.get('ProviderId')})

            
            page_index = self.env.company.provider_page_index + 1 if self.env.company.provider_current_count + 15 < response_json['TotalCount'] else -1
            current_count = self.env.company.provider_current_count + 15 if self.env.company.provider_current_count + 15 < response_json['TotalCount'] else 0

            self.env.company.sudo().write({'provider_page_index': page_index, 'provider_current_count':current_count})



#         result = {
#             "type": "ir.actions.act_window",
#             "res_model": "dentalor.provider",
#             "name": _("Dentalor Employees"),
#             'view_mode': 'tree,form',
#         }
#         return result

# access_dentalor_provider,dentalor_provider.dentalor_provider,model_dentalor_provider,base.group_user,1,1,1,1
