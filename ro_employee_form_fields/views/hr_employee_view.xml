<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <data>
        <record id="employee_form_fields_inherited" model="ir.ui.view">
            <field name="name">employee.form.fields.inherit</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='work_email']" position="after">
                    <field name="ro_employee_ar_name"></field>
                    <field name="ro_employee_hiring_date"></field>

                </xpath>
                <!-- work information -->
                <xpath expr="//field[@name='work_location_id']" position="after">
                    <field name="ro_doctor_category_fees"></field>
                    <field name="ro_last_increment_date"></field>  
                    <field name="ro_reason_of_increment"></field> 
                    <field name="ro_payment_method"></field> 

                    
                   
                        
                                
                </xpath>

                <xpath expr="//group[@name='departure']" position="after">
                    <group name="check_boxes" string="Check box eligible for">
                        <field name="ro_insurance"/>
                        <field name="ro_tax"/>
                        <field name="ro_overtime"/>
                    </group>
                </xpath>


               
                <!-- private information -->
                 <xpath expr="//field[@name='lang']" position="after">
                    <field name="ro_religion"></field>
                    <field name="ro_garaduation_date"></field>
                    
                   
                   

                </xpath>

                <xpath expr="//page[@name='personal_information']//group" position="after">
                <group name="accouunts" string="Accounts">
                    <field name="account_line_ids" widget="one2many" context="{'show_attribute': False}">
                        
                            <tree string="Accounts" editable="bottom">
                                <field name="ro_bank_name"/>
                                <field name="ro_customer_id"/>
                                <field name="ro_account_number"/>
                                <field name="ro_branch_number"/> 
                                <field name="ro_english_name"/>       

                            </tree>
                            
                        </field> 
                        </group>
                      
                </xpath>    

                
           </field>     
          </record>
  </data>
</odoo>