# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class Hr(models.Model):
    _inherit = 'hr.employee'
    

    ro_employee_ar_name = fields.Char(string='Arabic Name')
    ro_employee_hiring_date = fields.Date(string='Hiring Date')
    
    #work info
    ro_doctor_category_fees = fields.Many2one('dr.fees', string='Doctor Category Fees')
    ro_last_increment_date = fields.Date(string='Last Increment date')
    ro_reason_of_increment  = fields.Char(string=' Reason Of Increment ')
    ro_payment_method = fields.Selection([
        ('cash', 'Cash'),
        ('bank', 'Bank')
    ], string='Payment Method')

    ro_insurance = fields.Boolean(string='Insurance')
    ro_tax = fields.Boolean(string='Tax')
    ro_overtime = fields.Boolean(string='Overtime')



    
    #private info
    ro_religion = fields.Selection([
        ('muslim', 'Muslim'),
        ('christian', 'Christian')
    ], string='Religion')

    ro_garaduation_date = fields.Date(string='Graduation date')

    account_line_ids = fields.One2many('accounts.lines','accounts_id',string="Accounts Lines")