from odoo import fields, models,api,_


class AccountsLines(models.Model):
    _name = "accounts.lines"
    _description = "lines"

    name = fields.Char(string='name')
    accounts_id = fields.Many2one('hr.employee', string='Accounts')
    ro_bank_name = fields.Char(string='Bank Name')
    ro_customer_id = fields.Char(string='Customer ID')
    ro_account_number = fields.Char(string='Account Number')
    ro_branch_number = fields.Char(string='Branch Number')
    ro_english_name = fields.Char(string='English Name')

     

