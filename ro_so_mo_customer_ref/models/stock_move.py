# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class StockMove(models.Model):
    _inherit = 'stock.move'
    
    ro_client_so_ref = fields.Char(string="SO Customer Reference", copy=False)
    ro_so_partner_id = fields.Many2one('res.partner', string="SO Partner", copy=False)
    ro_so_id = fields.Many2one('sale.order', string="SO", copy=False )
    

    def _prepare_procurement_values(self):
        res = super()._prepare_procurement_values()
        res['ro_client_so_ref'] = self.sale_line_id.order_id.client_order_ref or self.raw_material_production_id.ro_client_order_ref
        res['ro_so_partner_id'] = self.sale_line_id.order_id.partner_id.id or self.raw_material_production_id.ro_partner_id.id
        res['ro_so_id'] = self.sale_line_id.order_id.id or self.raw_material_production_id.ro_sale_id.id
        return res
    