# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class StockPicking(models.Model):
    _inherit = 'stock.picking'
    
    ro_client_order_ref = fields.Char(string="SO Customer Reference", related='move_ids.group_id.mrp_production_ids.ro_client_order_ref', copy=False, store=True)
    ro_partner_id = fields.Many2one('res.partner', string="SO Partner", related='move_ids.group_id.mrp_production_ids.ro_partner_id', copy=False, store=True)
    ro_sale_id = fields.Many2one('sale.order', string="SO", related='move_ids.group_id.mrp_production_ids.ro_sale_id', copy=False, store=True)
