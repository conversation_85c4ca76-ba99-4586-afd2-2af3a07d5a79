# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class StockRule(models.Model):
    _inherit = 'stock.rule'
    
    def _get_custom_move_fields(self):
        fields = super(StockRule, self)._get_custom_move_fields()
        fields += ['ro_client_so_ref']
        fields += ['ro_so_partner_id']
        fields += ['ro_so_id']

        return fields
    
    
    def _prepare_mo_vals(self, product_id, product_qty, product_uom, location_dest_id, name, origin, company_id, values, bom):
        res = super()._prepare_mo_vals(product_id, product_qty, product_uom, location_dest_id, name, origin, company_id, values, bom)
     
        if values.get('ro_so_id'):
            res['ro_client_order_ref'] = values.get('ro_client_so_ref')
            res['ro_partner_id'] = values.get('ro_so_partner_id')
            res['ro_sale_id'] = values.get('ro_so_id')
        else:
            sale_order = self.env['sale.order'].search([('name','=', origin)])
            res['ro_client_order_ref'] = sale_order.client_order_ref
            res['ro_partner_id'] = sale_order.partner_id.id
            res['ro_sale_id'] = sale_order.id
        
        return res
    
    