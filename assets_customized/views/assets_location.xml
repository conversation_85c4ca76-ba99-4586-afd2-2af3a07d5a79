<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="account_assets_locations" model="ir.actions.act_window">
        <field name="name">Assets Location</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.asset.location</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add New Assets Location
            </p>
        </field>
    </record>

    <!--    <form view-->
    <record id="assets_location_form" model="ir.ui.view">
        <field name="name">account.assets.location.form</field>
        <field name="model">account.asset.location</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                        </group>
                    </group>
                </sheet>

                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <!--                    <field name="activity_ids"/>-->
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <menuitem id="menu_assets_location"
              name="Assets Location"
              parent="account.menu_finance_entries_management"
              action="account_assets_locations"/>
</odoo>