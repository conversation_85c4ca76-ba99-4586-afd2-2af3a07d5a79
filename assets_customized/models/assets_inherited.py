from odoo import models, fields, api, _
from datetime import datetime


class AssetsInherited(models.Model):
    _inherit = "account.asset"

    res_name = fields.Many2one('res.partner', string='Responsible Name', tracking=True)
    location = fields.Many2many('account.asset.location', string='Location',
                                tracking=True)
    Log_text = fields.Text(string='Log', readonly=True, copy=False)



    @api.model
    def create(self, vals):
        res = super(AssetsInherited, self).create(vals)

        log_text = ''

        if res.res_name:
            log_text = f"First By : {res.res_name.name} \n" 

        if res.location:
            log_text += f"First Location : {res.location.name} \n"

        res['Log_text'] = log_text + '\n'

        return res


    def write(self, vals):
        
        current_login = self.env.user

        curDT = datetime.now()
        date_time = curDT.strftime("%d-%m-%Y, %H:%M:%S")
        
        data = ''

        if 'res_name' in vals:
            str_name = f"{self.res_name.name}  Changed To ----->  {self.env['res.partner'].browse(vals['res_name']).name}  at {date_time}  by User {current_login.name}"
            data += str_name + """\n"""

        if 'location' in vals:
            str_location = f"""[{' - '.join(self.location.mapped('name'))}] Changed To -----> 
                           [{' - '.join(self.env['account.asset.location'].browse((vals['location'][0][2])).mapped('name'))}]  at {date_time} by User {current_login.name}"""

            data += str_location + """\n"""

        if data:
            vals['Log_text'] = str(self.Log_text) + data + '\n'

        res = super(AssetsInherited, self).write(vals)
        return res
