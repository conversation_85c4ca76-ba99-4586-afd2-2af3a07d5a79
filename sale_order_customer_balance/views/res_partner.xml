<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="res_partners_form_view_inherit" model="ir.ui.view">
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='vat']" position="after">
                    <field name="credit_limit" readonly="1" force_save="1"/>
                    <field name="ro_pass_credit_limit" readonly="1" force_save="1" widget="boolean_toggle"/>
                </xpath>
            </field>
        </record>
        <record id="res_partners_form_view_inherit_extra_rights" model="ir.ui.view">
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="sale_order_customer_balance.res_partners_form_view_inherit"/>
            <!-- <field name="groups_id" eval="[(4, ref('sale_order_customer_balance.group_sales_customer_limit'))]"/> -->
            <field name="arch" type="xml">
                <xpath expr="//field[@name='credit_limit']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath>
                <xpath expr="//field[@name='ro_pass_credit_limit']" position="attributes">
                    <attribute name="readonly">0</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
