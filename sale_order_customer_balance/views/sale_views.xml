<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>


        <record id="egy_trade_sale_order_total_due_inherit" model="ir.ui.view">
            <field name="name">sale.order.total.due</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name="partner_currency_id" invisible="1"/>
                    <field name="total_due" widget='monetary' options="{'currency_field': 'partner_currency_id'}" invisible='1'/>
                    <field name="new_total_due"/>
                    <field name="so_credit_limit" readonly="1" widget='monetary' options="{'currency_field': 'partner_currency_id'}"/>
                    <field name="so_credit_remaining" readonly="1" widget='monetary' options="{'currency_field': 'partner_currency_id'}"/>
                    <field name="credit_check" readonly="1" force_save="1"/>
                    <field name="difference_amount" readonly='1' force_save='1'/>
                    <field name="user_checked_id" invisible='1'/>
                </xpath>
            </field>
        </record>

        <record id="sale_order_total_due_inherit_extra_rights" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_order_customer_balance.egy_trade_sale_order_total_due_inherit"/>
            <!-- <field name="groups_id" eval="[(4, ref('sale_order_customer_balance.group_sales_credit_check'))]"/> -->
            <field name="arch" type="xml">
                <xpath expr="//field[@name='credit_check']" position="attributes">
                    <attribute name="readonly">0</attribute>
                    <attribute name="attrs">{'readonly':[('state','in',('sale','done'))]}</attribute>
                </xpath>
            </field>
        </record>

        <record id="sale_order_due_tree_view_inherit" model="ir.ui.view">
            <field name="name">sale.order.check.tree</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_quotation_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='state']" position="after">
                    <field name="credit_check" readonly='1' force_save='1'/>
                    <field name="difference_amount" readonly='1' force_save='1'/>
                </xpath>
            </field>
        </record>

        <record id="account_move_total_due_inherit" model="ir.ui.view">
            <field name="name">account.move.total.due</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='sale_info_group']" position="inside">
                    <field name="credit_check" groups="sales_team.group_sale_manager" readonly='1' force_save='1'/>
                    <field name="user_checked_id" readonly='1' force_save='1'/>
                    <field name="difference_amount" readonly='1' force_save='1'/>
                </xpath>
            </field>
        </record>

        <record id="account_move_total_due_tree_inherit" model="ir.ui.view">
            <field name="name">account.move.tree.total.due</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='state']" position="after">
                    <field name="credit_check" readonly='1' force_save='1'/>
                    <field name="user_checked_id" readonly='1' force_save='1'/>
                    <field name="difference_amount" readonly='1' force_save='1'/>
                    <field name="returned" readonly='1' force_save='1'/>
                </xpath>
                <xpath expr="//field[@name='payment_state']" position="before">
                    <field name="paid_amount" readonly='1' force_save='1'/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>