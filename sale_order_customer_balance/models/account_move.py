# -*- coding: utf-8 -*-

import json

from odoo import models, fields, api, _

class AccountMove(models.Model):
    _inherit = 'account.move'

    credit_check = fields.Boolean(string='Credit', default=False)
    user_checked_id = fields.Many2one('res.users')
    difference_amount = fields.Float()

    returned = fields.Boolean()

    paid_amount = fields.Float(compute="_compute_amount")

    def write(self, values):

        sale_order_diff = sum(self.invoice_line_ids.sale_line_ids.order_id.mapped('difference_amount'))

        tax_totals_json = json.loads(values.get('tax_totals_json')) if values.get('tax_totals_json') else False

        if tax_totals_json != 0 and sale_order_diff != 0 and sale_order_diff > tax_totals_json.get('amount_total'):
            
            values['difference_amount'] = tax_totals_json.get('amount_total')
        
        result = super().write(values)

        return result

    def _compute_amount(self):
        super(Account<PERSON>ove, self)._compute_amount()

        for this in self:
            this.paid_amount = this.amount_total - this.amount_residual

            if (this.payment_state == 'paid' or (this.amount_residual == 0 and this.payment_state == 'in_payment')) and not this.returned:
                this.returned = True
