# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools.misc import formatLang, format_date, get_lang
import warnings


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    partner_currency_id = fields.Many2one(
        'res.currency', related='partner_id.currency_id', string="Currency")
    total_due = fields.Monetary(
        string='Total Due', related='partner_id.total_due')
    new_total_due = fields.Monetary(
        compute='_compute_new_total_due', string='Total Due')

    credit_check = fields.Boolean(
        string='Credit',
        default=False,
        tracking=8
    )

    so_credit_limit = fields.Float(string='Credit Limit', related='partner_id.credit_limit')
    so_credit_remaining = fields.Float(string='Credit Remaining', compute='_compute_so_credit_remaining')

    difference_amount = fields.Float()

    user_checked_id = fields.Many2one('res.users')

    @api.depends('so_credit_limit', 'new_total_due')
    def _compute_so_credit_remaining(self):
        for res in self:
            if res.so_credit_limit == 0:
                res.so_credit_remaining = res.so_credit_limit
            elif res.new_total_due < 0:
                res.so_credit_remaining = res.so_credit_limit
            else:
                res.so_credit_remaining = res.so_credit_limit - res.new_total_due

            if res.so_credit_remaining < 0:
                res.so_credit_remaining = 0

    @api.depends('total_due')
    def _compute_new_total_due(self):
        for o in self:

            to_invoice_sol_records = self.env['sale.order.line'].search(
                [('order_partner_id', '=', o.partner_id.id), ('state', 'in', ['sale', 'done'])])

            so_subtotal_sum = 0
            total_draft_aml_out = 0
            total_draft_aml_in = 0

            to_invoice_sol_records = to_invoice_sol_records.filtered(
                lambda l: l.qty_invoiced < l.product_uom_qty)

            for line in to_invoice_sol_records:
                # because draft invoice not in total_due but invoiced in so
                total_draft_aml_out += sum(line.invoice_lines.filtered(
                    lambda l: l.parent_state == 'draft' and l.move_id.move_type in
                    ['in_invoice', 'out_refund']).mapped('price_total'))
                total_draft_aml_in += sum(line.invoice_lines.filtered(
                    lambda l: l.parent_state == 'draft' and l.move_id.move_type in
                    ['out_invoice', 'in_refund']).mapped('price_total'))

                qty_and_price = (line.product_uom_qty -
                                 line.qty_invoiced) * line.price_unit

                after_discount = ((1-(line.discount/100)) * qty_and_price)
                so_subtotal_sum += after_discount + \
                    ((sum(line.tax_id.mapped('amount'))/100) * after_discount)

            o.new_total_due = o.total_due + so_subtotal_sum - o.total_parent_load + \
                total_draft_aml_in - total_draft_aml_out

    @api.onchange('credit_check')
    def _update_diff_user(self):
        for this in self:

            # - this.partner_id.ro_bander_give

            if this.partner_id.credit_limit < this.new_total_due:
                difference_amount = this.amount_total
            else:
                difference_amount = this.amount_total + \
                    this.new_total_due - this.partner_id.credit_limit

            if this.credit_check and difference_amount > 0:
                this.difference_amount = difference_amount
                this.user_checked_id = self.env.user.id
                
            elif this.credit_check:
                this.difference_amount = 0
                this.user_checked_id = False
                this.credit_check = False

                warning = {
                    'title': _("Warning For Credit Check"),
                    'message': _('Customer have credit limit.')
                }

                return {'warning': warning}
            else:
                this.difference_amount = 0
                this.user_checked_id = False
                this.credit_check = False

    def action_confirm(self):

        for this in self:
            if this.partner_id.ro_pass_credit_limit:
                return super(SaleOrder, this).action_confirm()

            if not this.credit_check and this.amount_total > 0 and this.amount_total > -this.new_total_due + this.partner_id.credit_limit:
                raise ValidationError(_('Customer balance not enough'))

            # if checked
            elif this.credit_check and this.user_checked_id and this.difference_amount < 0:
                raise ValidationError(
                    _('Customer & Manager balance not enough'))

            return super(SaleOrder, this).action_confirm()

    def _prepare_invoice(self):
        res = super(SaleOrder, self)._prepare_invoice()

        res['credit_check'] = self.credit_check
        res['user_checked_id'] = self.user_checked_id.id
        difference_amount = self.difference_amount - \
            sum(self.invoice_ids.mapped('difference_amount'))
        res['difference_amount'] = difference_amount

        return res
