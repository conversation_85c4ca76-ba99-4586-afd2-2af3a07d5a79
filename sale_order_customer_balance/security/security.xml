<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="group_sales_credit_check" model="res.groups">
            <field name="name">Credit Check [ShinyWhite]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
        <record id="group_sales_customer_limit" model="res.groups">
            <field name="name">Customer Limit [ShinyWhite]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
    </data>
</odoo>
