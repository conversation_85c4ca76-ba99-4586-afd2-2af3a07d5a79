# -*- coding: utf-8 -*-

import logging
from psycopg2 import sql

from odoo import models, fields, api
from odoo import tools

_logger = logging.getLogger(__name__)


class StockMoveLocation(models.Model):

    _name = "stock.move.location.filter"
    _description = "Location Moves Filter"
    _auto = False
    _order = "date asc, id asc"
    _check_company_auto = True

    description = fields.Char(
        string='Description',
        readonly=True
    )
    partner_id = fields.Many2one(
        comodel_name='res.partner',
        string='Partner',
        readonly=True
    )
    location_id = fields.Many2one(
        comodel_name='stock.location',
        string='Location',
        readonly=True
    )

    move_id = fields.Many2one('stock.move', readonly=True)

    location_name_to = fields.Char(
        string='Location To',
        readonly=True
    )
    location_name_from = fields.Char(
        string='Location From',
        readonly=True
    )
    product_id = fields.Many2one(
        comodel_name='product.product',
        string='Product',
        readonly=True
    )
    product_tmpl_id = fields.Many2one(
        comodel_name='product.template',
        string='Product Template',
        readonly=True
    )
    categ_id = fields.Many2one(
        comodel_name="product.category",
        string='Category',
        readonly=True
    )
    product_uom_id = fields.Many2one(
        comodel_name="uom.uom",
        string="Line UoM",
        readonly=True
    )
    uom_id = fields.Many2one(
        comodel_name="uom.uom",
        string="UoM",
        readonly=True
    )
    date = fields.Datetime(
        string='Date Planned',
        readonly=True
    )
    picking_id = fields.Many2one(
        comodel_name='stock.picking',
        string='Picking',
        readonly=True
    )
    company_id = fields.Many2one(
        comodel_name='res.company',
        string='Company',
        readonly=True
    )
    warehouse_id = fields.Many2one(
        comodel_name='stock.warehouse',
        string='Warehouse',
        readonly=True
    )
    qty_on_hand = fields.Float(
        string='Quantity On Hand',
        digits=(16, 2),
        readonly=True
    )
    qty_add = fields.Float(
        string='In',
        digits=(16, 2),
        readonly=True
    )
    qty_ded = fields.Float(
        string='Out',
        digits=(16, 2),
        readonly=True
    )
    qty_current = fields.Float(
        string='Balance',
        digits=(16, 2),
        readonly=True,
        group_operator=False
    )

    move_type = fields.Char(
        string='Move Type',
        readonly=True
    )
    value = fields.Float(
        string='Value',
        readonly=True,
        digits=(16, 2),
        group_operator=False
    )

    total_value = fields.Float(
        string='Total Value',
        digits=(16, 2),
        readonly=True
    )
    total_value_balance = fields.Float(
        string='Total Value Balance',
        digits=(16, 2),
        readonly=True,
        group_operator=False
    )

    def _view_internal(self, date_to_filter):
        view_str = """
            

            with stock_valuation_layer_total as (
            select  min(distinct svl.id) id ,  sum(distinct svl.value) valuation_value, min(distinct svl.stock_move_id) move_id, min(distinct svl.product_id) product_id, min(distinct svl.company_id) company_id from stock_valuation_layer svl
                left join stock_move sm on sm.id = svl.stock_move_id 
                left join stock_scrap ss on svl.product_id = ss.product_id and sm.picking_id = ss.picking_id
                group by svl.stock_move_id
            )
            
            %s

            
        
        """ % (self._view_internal_to(date_to_filter))

        return view_str

    def _view_internal_old(self, date_to_filter):
        view_str = """

        
        select
        
            distinct 
            max(id),
            CAST(NULL AS int) as move_id,
            CAST(NULL AS int) location_id,
            product_id,
            product_tmpl_id,
            CAST(NULL AS int) as uom_id,
            CAST(NULL AS int) as product_uom_id,
            case when sum(qty_on_hand) <=0 then 0

            else sum(total_value)/sum(qty_on_hand)
            end as vale,
            warehouse_id,
            CAST(NULL AS int) as partner_id,
            CAST('Initial Balance' AS varchar) as description,
            sum(qty_on_hand) as qty_on_hand,
            sum(qty_on_hand) as qty_current_sum,

            sum(qty_add) as qty_add,
            sum(qty_ded) as qty_ded,
            sum(total_value) as total_value,
            sum(total_value) as total_value_balance_sum,
            %s::timestamp without time zone as date,
            CAST(NULL AS int) as picking_id,
            company_id,
            CAST(NULL AS varchar) as location_name_from,
            CAST(NULL AS varchar) as location_name_to,
            categ_id,
            CAST(NULL AS varchar) as move_type
            
            
            
            from

            (

                select
                distinct *,
                sum(qty_current_sum) over (partition by product_id, warehouse_id order by date asc, id asc rows between 
                unbounded preceding and current row) as qty_current
                from

                (

                    select
                    concat('inblc',sml.id) id,
                    sml.move_id,
                    null location_id,
                    sml.product_id,
                    pr.id product_tmpl_id,
                    uom_template.id uom_id,
                    sml.product_uom_id,
                    coalesce (svlt.valuation_value / sm.product_qty,0) as value,
                    wh.id as warehouse_id,
                    coalesce (sm.partner_id,
                    sp.partner_id) as partner_id,
                    sml.reference as description,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_on_hand,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_current_sum,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) qty_add,
                    0 qty_ded,
                    case
                        when sll.usage = 'internal' and spt.code = 'internal'
                                        then 0
                                        
                        else
                                    coalesce (svlt.valuation_value/sm.product_qty, 0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))  
                    end as total_value,
                    case
                        when sll.usage = 'internal' and spt.code = 'internal'
                                        then 0
                                        
                        else
                                    coalesce (svlt.valuation_value/sm.product_qty, 0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))  
                    end as total_value_balance_sum,
                    sml.date,
                    sml.picking_id,
                    sl.company_id,
                    sll.complete_name as location_name_from ,
                    sl.complete_name as location_name_to,
                    pr.categ_id as categ_id,
                    /*to_char(sm.location_id ,'999')  as location_name_from,*/
                    case
                        when 
                            sll.usage != 'internal' and spt.code = 'internal'
                            then 'incoming' 
                        else coalesce(spt.code,'incoming') end move_type

                        from
                                stock_move_line sml
                        left join stock_move sm on
                                sm.id = sml.move_id
                                --left join stock_valuation_layer svl ON sm.id=svl.stock_move_id
                        left join stock_location sll on
                                sml.location_id = sll.id
                        left join stock_location sl on
                                sml.location_dest_id = sl.id
                        left join stock_picking sp on
                                sp.id = sml.picking_id
                                
                                left join stock_warehouse wh on
                            wh.view_location_id = CAST(split_part(sl.parent_path,'/',2) as int)

                        left join stock_picking_type spt on
                                sp.picking_type_id = spt.id
                        left join product_product pp on
                                sml.product_id = pp.id
                        left join product_template pr on
                                                pp.product_tmpl_id = pr.id
                        left join stock_valuation_layer_total svlt on
                                svlt.product_id = pp.id and svlt.move_id = sm.id
                        left join uom_uom uom on
                                                sml.product_uom_id = uom.id	        
                        left join uom_uom uom_template on
                                uom_template.id = pr.uom_id
                        


                        where
                    sl.usage = 'internal'
                    and sml.state != 'cancel'
                    and sml.company_id = sl.company_id
                    and sm.product_qty != 0
                    and sml.state = 'done'
                        %s
                        union all

                        select
                    concat('outblc',sml.id) id,
                    sml.move_id,
                    null location_id ,
                    sml.product_id,
                    pr.id product_tmpl_id,
                    uom_template.id uom_id,
                    sml.product_uom_id,
                    coalesce (svlt.valuation_value / sm.product_qty,0) as value,
                    wh.id as warehouse_id,
                    coalesce (sm.partner_id,
                    sp.partner_id) as partner_id,
                    sml.reference as description,
                    -(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_on_hand,
                    -(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_current_sum,
                    0 qty_add,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) qty_ded,
                    case
                        when sll.usage = 'internal' and spt.code = 'internal'
                            then 0
                        
                        else
                            coalesce (svlt.valuation_value/sm.product_qty,0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                                        
                    end as total_value,
                    case
                        when sll.usage = 'internal' and spt.code = 'internal'
                            then 0
                        
                        else
                            coalesce (svlt.valuation_value/sm.product_qty,0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                                        
                    end as total_value_balance_sum,
                    sml.date,
                    sml.picking_id,
                    sl.company_id,
                    /*to_char(sm.location_dest_id,'999') as location_name_to,*/
                    sl.complete_name as location_name_from,
                                sll.complete_name as location_name_to,
                    pr.categ_id as categ_id,
                    case
                        when 
                            sll.usage != 'internal' and spt.code = 'internal'
                            then 'outgoing' 
                        else coalesce(spt.code,'outgoing') end move_type

                        from
                                stock_move_line sml
                        left join stock_move sm on
                                sm.id = sml.move_id
                                --left join stock_valuation_layer svl ON sm.id=svl.stock_move_id
                        left join stock_location sll on
                                sml.location_dest_id = sll.id
                        left join stock_location sl on
                                sml.location_id = sl.id
                        left join stock_picking sp on
                                sp.id = sml.picking_id
                        left join stock_warehouse wh on
                            wh.view_location_id = CAST(split_part(sl.parent_path,'/',2) as int)

                                left join stock_picking_type spt on
                                sp.picking_type_id = spt.id
                        left join product_product pp on
                                sml.product_id = pp.id
                        left join product_template pr on
                                                pp.product_tmpl_id = pr.id
                                                left join stock_valuation_layer_total svlt on
                                svlt.product_id = pp.id and svlt.move_id = sm.id
                        left join uom_uom uom on
                                                sml.product_uom_id = uom.id
                        left join uom_uom uom_template on
                                uom_template.id = pr.uom_id


                        

                        where
                                sl.usage = 'internal'
                        and sml.state != 'cancel'
                        and sml.company_id = sl.company_id
                        and sm.product_qty != 0
                        and sml.state = 'done'  
                        %s
                        
                        union all
                        
                        select
                    concat('othblc',svl.id) id,
                    null move_id,
                    null location_id,
                    svl.product_id,
                    null product_tmpl_id,
                    null uom_id,
                    null product_uom_id,

                    case 
                    when svl.quantity >= 0
                    then svl.unit_cost 
                    else
                        svl.unit_cost * -1
                    
                    end as value,
                    
                    null warehouse_id,
                    null as partner_id,
                    svl.description as description,
                    
                    svl.quantity as qty_on_hand,
                    svl.quantity as qty_current_sum,
                    
                    coalesce(case when svl.quantity >= 0
                    then quantity end,0) as qty_add,
                    
                    coalesce(case when svl.quantity < 0
                    then abs(quantity) end,0) as qty_ded,
                    
                    
                    value total_value,
                    value total_value_balance_sum,
                    
                    svl.create_date date,
                    null picking_id,
                    svl.company_id,
                    null location_name_from ,
                    null location_name_to,
                    null categ_id,
                    /*to_char(sm.location_id ,'999')  as location_name_from,*/
                    null as move_type
            
                
                from stock_valuation_layer svl where svl.stock_move_id is null
                %s

                ) sml3
            ) sml4
            
            
            group by product_id,product_tmpl_id,categ_id,warehouse_id,company_id
        """ % ("'%s'" % (date_to_filter) if date_to_filter else "'1990-01-01 00:00:00'", "and sml.date < '%s'" % (date_to_filter) if date_to_filter else "", "and sml.date < '%s'" % (date_to_filter) if date_to_filter else "", "and svl.create_date < '%s'" % (date_to_filter) if date_to_filter else "")
        # %("where date < '%s'"  % (date_to_filter) if date_to_filter else "")
        # if date_to_filter:
        #     view_str +
        return view_str

    def _view_internal_to(self, date_to_filter):
        view_str = """


        select
        distinct *,
        sum(total_value_balance_sum) over (partition by product_id, warehouse_id order by date asc, id asc rows between 
        unbounded preceding and current row) as total_value_balance
        from

        (

            select
            distinct *,
            sum(qty_current_sum) over (partition by product_id, warehouse_id order by date asc, id asc rows between 
            unbounded preceding and current row) as qty_current
            from

            (

                select
                concat('in',sml.id) id,
                sml.move_id,
                sl.id as location_id,
                sml.product_id,
                pr.id product_tmpl_id,
                uom_template.id uom_id,
                sml.product_uom_id,
                coalesce (svlt.valuation_value / sm.product_qty,0) as value,
                wh.id warehouse_id,
                coalesce (sm.partner_id,
                sp.partner_id) as partner_id,
                sml.reference as description,
                (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_on_hand,
                (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_current_sum,
                (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) qty_add,
                0 qty_ded,
                case
                    when sll.usage = 'internal' and spt.code = 'internal'
                                    then 0
                                    
                    else
                                coalesce (svlt.valuation_value/sm.product_qty, 0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))  
                end as total_value,
                case
                    when sll.usage = 'internal' and spt.code = 'internal'
                                    then 0
                                    
                    else
                                coalesce (svlt.valuation_value/sm.product_qty, 0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))  
                end as total_value_balance_sum,
                sml.date,
                sml.picking_id,
                sl.company_id,
                sll.complete_name as location_name_from ,
                sl.complete_name as location_name_to,
                pr.categ_id as categ_id,
                /*to_char(sm.location_id ,'999')  as location_name_from,*/
                case
                    when 
                        sll.usage != 'internal' and spt.code = 'internal'
                        then 'incoming' 
                    else coalesce(spt.code,'incoming') end move_type

                    from
                            stock_move_line sml
                    left join stock_move sm on
                            sm.id = sml.move_id
                            --left join stock_valuation_layer svl ON sm.id=svl.stock_move_id
                    left join stock_location sll on
                            sml.location_id = sll.id
                    left join stock_location sl on
                            sml.location_dest_id = sl.id
                    left join stock_picking sp on
                            sp.id = sml.picking_id

                    left join stock_warehouse wh on
                            wh.view_location_id = CAST(split_part(sl.parent_path,'/',2) as int)

                    left join stock_picking_type spt on
                            sp.picking_type_id = spt.id
                    left join product_product pp on
                            sml.product_id = pp.id
                    left join product_template pr on
                                            pp.product_tmpl_id = pr.id
                    left join stock_valuation_layer_total svlt on
                            svlt.product_id = pp.id and svlt.move_id = sm.id
                    left join uom_uom uom on
                                            sml.product_uom_id = uom.id	        
                    left join uom_uom uom_template on
                            uom_template.id = pr.uom_id
                    


                    where
                sl.usage = 'internal'
                and sml.state != 'cancel'
                and sml.company_id = sl.company_id
                and sm.product_qty != 0
                and sml.state = 'done'
                    %s
                    union all

                    select
                concat('out',sml.id) id,
                sml.move_id,
                sl.id as location_id ,
                sml.product_id,
                pr.id product_tmpl_id,
                uom_template.id uom_id,
                sml.product_uom_id,
                coalesce (svlt.valuation_value / sm.product_qty,0) as value,
                wh.id warehouse_id,
                coalesce (sm.partner_id,
                sp.partner_id) as partner_id,
                sml.reference as description,
                -(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_on_hand,
                -(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_current_sum,
                0 qty_add,
                (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) qty_ded,
                case
                    when sll.usage = 'internal' and spt.code = 'internal'
                        then 0
                    
                    else
                        coalesce (svlt.valuation_value/sm.product_qty,0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                                    
                end as total_value,
                case
                    when sll.usage = 'internal' and spt.code = 'internal'
                        then 0
                    
                    else
                        coalesce (svlt.valuation_value/sm.product_qty,0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                                    
                end as total_value_balance_sum,
                sml.date,
                sml.picking_id,
                sl.company_id,
                /*to_char(sm.location_dest_id,'999') as location_name_to,*/
                sl.complete_name as location_name_from,
                            sll.complete_name as location_name_to,
                pr.categ_id as categ_id,
                case
                    when 
                        sll.usage != 'internal' and spt.code = 'internal'
                        then 'outgoing' 
                    else coalesce(spt.code,'outgoing') end move_type

                    from
                            stock_move_line sml
                    left join stock_move sm on
                            sm.id = sml.move_id
                            --left join stock_valuation_layer svl ON sm.id=svl.stock_move_id
                    left join stock_location sll on
                            sml.location_dest_id = sll.id
                    left join stock_location sl on
                            sml.location_id = sl.id
                    left join stock_picking sp on
                            sp.id = sml.picking_id

                            left join stock_warehouse wh on
                            wh.view_location_id = CAST(split_part(sl.parent_path,'/',2) as int)

                            left join stock_picking_type spt on
                            sp.picking_type_id = spt.id
                    left join product_product pp on
                            sml.product_id = pp.id
                    left join product_template pr on
                                            pp.product_tmpl_id = pr.id
                                            left join stock_valuation_layer_total svlt on
                            svlt.product_id = pp.id and svlt.move_id = sm.id
                    left join uom_uom uom on
                                            sml.product_uom_id = uom.id
                    left join uom_uom uom_template on
                            uom_template.id = pr.uom_id


                    

                    where
                            sl.usage = 'internal'
                    and sml.state != 'cancel'
                    and sml.company_id = sl.company_id
                    and sm.product_qty != 0
                    and sml.state = 'done'  
                    %s
                    
                    union all
                    
                    select
                concat('oth',svl.id) id,
                null move_id,
                null location_id,
                svl.product_id,
                null product_tmpl_id,
                null uom_id,
                null product_uom_id,

                case 
                when svl.quantity >= 0
                then svl.unit_cost 
                else
                    svl.unit_cost * -1
                
                end as value,
                
                null warehouse_id,
                null as partner_id,
                svl.description as description,
                
                svl.quantity as qty_on_hand,
                svl.quantity as qty_current_sum,
                
                coalesce(case when svl.quantity >= 0
                then quantity end,0) as qty_add,
                
                coalesce(case when svl.quantity < 0
                then abs(quantity) end,0) as qty_ded,
                
                
                value total_value,
                value total_value_balance_sum,
                
                svl.create_date date,
                null picking_id,
                svl.company_id,
                null location_name_from ,
                null location_name_to,
                null categ_id,
                /*to_char(sm.location_id ,'999')  as location_name_from,*/
                null as move_type
                from stock_valuation_layer svl where svl.stock_move_id is null
                %s

                
                /*union with initial balance*/
                union all
                select * from(%s) as sub_balance
        
            
            

            ) sml
        ) sml2  
        """ % ("and sml.date >= '%s'" % (date_to_filter) if date_to_filter else "", "and sml.date >= '%s'" % (date_to_filter) if date_to_filter else "", "and svl.create_date >= '%s'" % (date_to_filter) if date_to_filter else "" , self._view_internal_old(date_to_filter))
        return view_str

    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        date_to_filter = self._context.get('date_filter')

        self.env.cr.execute(sql.SQL("CREATE or REPLACE VIEW {} as ({})").format(
            sql.Identifier(self._table), sql.SQL(self._view_internal(date_to_filter))))
