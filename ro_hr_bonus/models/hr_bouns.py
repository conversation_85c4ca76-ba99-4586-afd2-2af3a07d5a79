from odoo import fields, models,api,_
from datetime import datetime, timedelta

class Bouns(models.Model):
    _name = "hr.bouns"
    _description = "Bonus"

    name = fields.Char(string='name')
    ro_bouns_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_bouns_date = fields.Date(string='Date', default=fields.Date.today)

    ro_bouns_type = fields.Selection([('days', 'Days'), ('amount', 'Amount')], string='Type', required=True)
    ro_bouns_days = fields.Float(string='Days')
    ro_bouns_amount = fields.Float(string='Amount')
    ro_bouns_reason_type = fields.Many2one('hr.payslip.input.type', string='Reason Type', domain=[('is_bonus','=',True)])
    ro_bouns_note = fields.Char(string='Note')
     
    state = fields.Selection([
        ('new', 'New'),
        ('approved', ' Approved'),
        ('refused', 'Refused'),
    ],required=True, default='new', copy=False, tracking=True)  

    @api.model
    def create(self, vals):
        vals['name'] = self.env['ir.sequence'].next_by_code('bonus_seq')
        return super(Bouns, self).create(vals)

    def action_approved(self):
        self.state = 'approved'

    def action_refused(self):
        self.state = 'refused'    
    
             
           
       
    
    
