from odoo import fields, Command, models, api, _


class HrPayslipInput(models.Model):
    _inherit = 'hr.payslip.input'
    
    bouns_ids = fields.Many2many('hr.bouns', string='Bonus')

    no_days = fields.Float("Days")

class Payslip(models.Model):
    _inherit = 'hr.payslip'

    ro_payslip_bouns_days = fields.Float('bonus')
    ro_payslip_bouns_amount = fields.Float('bonus')

    bouns_ids = fields.Many2many('hr.bouns', string='bonus',compute='_compute_bouns',store=True)

    @api.depends('employee_id', 'date_from', 'date_to')
    def _compute_bouns(self):
        for payslip in self:
            if payslip.employee_id:
                bouns = self.env['hr.bouns'].search([
                    ('ro_bouns_employee', '=', payslip.employee_id.id),
                    ('ro_bouns_date', '>=', payslip.date_from),
                    ('ro_bouns_date', '<=', payslip.date_to),
                    ('state', '=', 'approved'),
                ])
                bouns_days = bouns.filtered(lambda x: x.ro_bouns_type == 'days')
                bouns_amount = bouns.filtered(lambda x: x.ro_bouns_type == 'amount')
                
                payslip.bouns_ids = bouns.ids
                payslip.ro_payslip_bouns_days = sum(bouns_days.mapped('ro_bouns_days'))
                payslip.ro_payslip_bouns_amount = sum(bouns_amount.mapped('ro_bouns_amount'))
                
                
    def compute_sheet(self):
        for payslip in self:
            payslip._compute_bouns()
            lines_to_remove = payslip.input_line_ids.filtered(lambda x: x.bouns_ids or x.input_type_id.is_bonus)
            input_line_vals = [Command.unlink(line.id) for line in lines_to_remove]

            bouns_types = payslip.bouns_ids.mapped('ro_bouns_reason_type')
            for type in bouns_types:
                bounses_filtered = payslip.bouns_ids.filtered(lambda x:x.ro_bouns_reason_type == type)
                input_line_vals.append(Command.create({
                    'name': type.name,
                    'amount': sum(bounses_filtered.mapped('ro_bouns_amount')),
                    'no_days': sum(bounses_filtered.mapped('ro_bouns_days')),
                    'input_type_id': type.id,
                    'bouns_ids': bounses_filtered.ids,
                }))
            payslip.update({'input_line_ids': input_line_vals})
        return super(Payslip, self).compute_sheet()
    
