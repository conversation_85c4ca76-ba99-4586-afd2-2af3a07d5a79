<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record  id="hr_bouns_group" model="res.groups">
            <field name="name">Show bonus group[R&amp;D]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
         <record  id="bouns_approved_refused_group_group" model="res.groups">
            <field name="name">approved and refused bonus[R&amp;D]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
       
    </data>
</odoo>
