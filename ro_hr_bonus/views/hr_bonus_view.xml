<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <data>

        <record id="hr_employee_bouns_tree_view" model="ir.ui.view">
            <field name="name">hr.bouns.tree</field>
            <field name="model">hr.bouns</field>
            <field name="arch" type="xml">
                <tree string="Hr Bouns">
                   
                    <field name="name"/>
                    <field name="ro_bouns_employee"/>
                    <field name="ro_bouns_date"/>
                    <field name="state" widget="badge" select="1" readonly="1"/>
                    
                </tree>
            </field>
        </record>

        <record id="hr_bouns_form_view" model="ir.ui.view">
            <field name="name">hr.bouns.form</field>
            <field name="model">hr.bouns</field>
            <field name="arch" type="xml">
            <form string="Hr Bonus">
                <header>
                <button name="action_approved" string="Approved" type="object" groups="ro_hr_bonus.bouns_approved_refused_group_group"  attrs="{'invisible': ['|',('state', '=', 'approved'),('state', '=', 'refused')]}"/>
                <button name="action_refused" string="Refused" type="object" groups="ro_hr_bonus.bouns_approved_refused_group_group"  attrs="{'invisible': [('state', '=', 'refused')]}"/>

                <field name="state" widget="statusbar" select="1" readonly="1"/>
                </header>               
                <sheet>
                    <div class="oe_title">
                        <h1>
                          <field name="name" readonly="1"/>
                        </h1>        
                    </div>                     
                    <group>
                        <group name="bonus">
                            <!-- <field name="ro_employee" attrs="{'readonly': [('state', '=', 'second_approve')]}" /> -->
                            <field name="ro_bouns_employee"  attrs="{'readonly': [('state', '!=', 'new')]}" options='{"no_open": True, "no_create": True}'/>
                            <field name="ro_bouns_type" attrs="{'readonly': [('state', '!=', 'new')]}"/>
                            <field name="ro_bouns_days"  attrs="{'readonly': [('state', '!=', 'new')],'invisible': [('ro_bouns_type', '!=', 'days')]}"/>
                            <field name="ro_bouns_amount"  attrs="{'readonly': [('state', '!=', 'new')],'invisible': [('ro_bouns_type', '!=', 'amount')]}" /> 
                        </group>
                        <group name="bonus_detai">
                            <field name="ro_bouns_date" attrs="{'readonly': [('state', '!=', 'new')]}"/>
                            <field name="ro_bouns_reason_type" attrs="{'readonly': [('state', '!=', 'new')]}" options='{"no_open": True}' />
                            <field name="ro_bouns_note" attrs="{'readonly': [('state', '!=', 'new')]}"/>
                        </group>   
                    </group>
                </sheet>
            </form>
            </field>
        </record>
    
        <record id="hr_employee_bouns_action" model="ir.actions.act_window">
            <field name="name">Bonus</field>
            <field name="res_model">hr.bouns</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new hr bonus
                </p>
            </field>
        </record>

        <menuitem
                id="menu_hr_bouns"
                name="Bonus"
                action="hr_employee_bouns_action"
                parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
                sequence="122"
                groups="ro_hr_bonus.hr_bouns_group"/>
              
    </data>
</odoo>
