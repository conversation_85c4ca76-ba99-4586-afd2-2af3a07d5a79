# -*- coding: utf-8 -*-
from werkzeug.exceptions import NotFound
from werkzeug.utils import redirect

from odoo import http, _
from odoo.http import request
from odoo.osv import expression

from odoo.addons.website.controllers import form
# from odoo.addons.website_helpdesk.controllers.main import WebsiteForm

from odoo.addons import website_helpdesk



class WebsiteForm(form.WebsiteForm):
     def extract_data(self, model, values):
          data = super(WebsiteForm, self).extract_data(model, values)
          if model.model == 'helpdesk.ticket':
            data['record'].update({'agent_name': request.env.user.id})
            data['record'].update({'user_id': request.env.user.id})
          return data
     
# HelpdeskTicketWebsiteForm = website_helpdesk.controllers.main.WebsiteForm
# def _handle_website_form(self, model_name, **kwargs):
#         email = request.params.get('partner_email')
#         patient_id = request.params.get('patient_id')
#         if email:
#             if request.env.user.email == email:
#                 partner = request.env.user.partner_id
#             else:
#                 partner = request.env['res.partner'].sudo().search([('email', '=', email)], limit=1)
#             if not partner:
#                 partner = request.env['res.partner'].sudo().create({
#                     'email': email,
#                     'name': request.params.get('partner_name', False),
#                     'lang': request.lang.code,
#                 })
#             request.params['partner_id'] = partner.id
#         kwargs['agent_name'] = request.env.user.id
#         kwargs['user_id'] = request.env.user.id

#         # if patient_id:
#         #     if request.env.user.patient_id == patient_id:
#         #         partner = request.env.user.partner_id

#         #     else:
#         #         partner = request.env['res.partner'].sudo().search([('patient_id', '=', patient_id)], limit=1)
#         #     if not partner:
#         #         partner = request.env['res.partner'].sudo().create({
#         #             'patient_id': patient_id,
#         #             'name': request.params.get('partner_name', False),
#         #             'lang': request.lang.code,
#         #         })
#         #     request.params['partner_id'] = partner.id

#         return super(HelpdeskTicketWebsiteForm, self)._handle_website_form(model_name, **kwargs)

# HelpdeskTicketWebsiteForm._handle_website_form = _handle_website_form


