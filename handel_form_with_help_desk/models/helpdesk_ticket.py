# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, SUPERUSER_ID


class HelpdeskTicket(models.Model):
    _inherit = 'helpdesk.ticket'

    patient_id = fields.Char(string="Patient ID")
    agent_name = fields.Many2one('res.users', string="Agent Name")
    manager_approval = fields.Many2one('ro.managers.approval', string="Manager Approval")


    # @api.model
    def create(self, values):
        
        if 'patient_id' in values:
            patient_id = values.get('patient_id')

            if patient_id:
                if self.env.user.patient_id == patient_id:
                    partner = self.env.user.partner_id

                else:
                    partner = self.env['res.partner'].sudo().search([('patient_id', '=', patient_id)], limit=1)
                if not partner:
                    partner = self.env['res.partner'].sudo().create({
                        'patient_id': patient_id,
                        'name': values.get('partner_name', False),
                    })
                values['partner_id'] = partner.id
                values['agent_name'] = values.get('agent_name', False)
                values['user_id'] = values.get('user_id', False)


        result = super(HelpdeskTicket, self).create(values)

        if 'manager_approval' in values:
            email = self.env['ro.managers.approval'].search([('id', '=', values.get('manager_approval'))])
            base_url = self.env['ir.config_parameter'].with_user(SUPERUSER_ID).get_param('web.base.url')
            self.env['mail.mail'].sudo().create({
                    'body_html': 'Ticket has been created for it service please check it, \n ticket link:'+ base_url+ result.access_url,
                    'subject': 'IT Service Request',
                    'email_to': email.name
                }).send()
        return result
    

    def write(self, values):
        if 'patient_id' in values:
            patient_id = values.get('patient_id')
            partner = False

            if patient_id:
                if self.env.user.patient_id == patient_id:
                    partner = self.env.user.partner_id

                else:
                    partner = self.env['res.partner'].sudo().search([('patient_id', '=', patient_id)], limit=1)
                # if not partner:
                #     partner = self.env['res.partner'].sudo().create({
                #         'patient_id': patient_id,
                #         'name': values.get('partner_name', False),
                #         # 'lang': self.lang.code,
                #     })
                if partner:
                    values['partner_id'] = partner.id
        result = super(HelpdeskTicket, self).write(values)
    
        return result
    
    @api.model
    def get_patient_info(self, patient_id=False):
        patients = self.env['res.partner'].sudo().search([('patient_id', '=', patient_id)])
        if patients:
            return {
                'pname': patients[0].name,
                'pphone': patients[0].phone,
            }
        else:
            return False
    
