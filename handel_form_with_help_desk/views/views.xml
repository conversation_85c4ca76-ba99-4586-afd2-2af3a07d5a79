<odoo>
  <data>
    <record id="add_patient_id_to_partner" model="ir.ui.view">
      <field name="model">res.partner</field>
      <field name="inherit_id" ref="base.view_partner_form"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='vat']" position="after">
          <field name="patient_id" />
        </xpath>
      </field>
    </record>

    <record id="search_by_patient_id" model="ir.ui.view">
      <field name="model">res.partner</field>
      <field name="inherit_id" ref="base.view_res_partner_filter"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='name']" position="after">
          <field name="patient_id" />
        </xpath>
      </field>
    </record>

    <record id="add_patient_id_to_ticket" model="ir.ui.view">
      <field name="model">helpdesk.ticket</field>
      <field name="inherit_id" ref="helpdesk.helpdesk_ticket_view_form"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='partner_id']" position="after">
          <field name="patient_id" />
        </xpath>

        <xpath expr="//field[@name='user_id']" position="after">
          <field name="agent_name" />
          <field name="manager_approval" 
          readonly="1"
          />
        </xpath>  
      </field>
    </record>

    <record id="search_by_patient_id_in_ticket" model="ir.ui.view">
      <field name="model">helpdesk.ticket</field>
      <field name="inherit_id" ref="helpdesk.helpdesk_tickets_view_search"/>
      <field name="arch" type="xml">
        <xpath expr="//field[@name='name']" position="after">
          <field name="patient_id" />
        </xpath>

        
      </field>
    </record>
  </data>
</odoo>