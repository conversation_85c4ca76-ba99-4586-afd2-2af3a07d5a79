<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>


        <record model="ir.ui.view" id="managers_email_form_view">
            <field name="name">arch.form</field>
            <field name="model">ro.managers.approval</field>
            <field name="arch" type="xml">
                <form string="Managers Email">
                    <sheet>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>
    
        <record id="view_managers_email_arc_post_search" model="ir.ui.view">
            <field name="name">managers_email.search</field>
            <field name="model">ro.managers.approval</field>
            <field name="arch" type="xml">
                <search string="Managers Email">
                    <field name="name" filter_domain="[('name','ilike',self)]" string="Managers Email"/>
                </search>
            </field>
        </record>
        <!-- explicit list view definition -->
        
        <record model="ir.ui.view" id="managers_email_list">
          <field name="name">ro.managers.approval.list</field>
          <field name="model">ro.managers.approval</field>
          <field name="arch" type="xml">
            <list string="Managers Email">
              <field name="name"/>
            </list>
          </field>
        </record>
        
        <!-- actions opening views on models -->
        
        <record id="managers_email_action_window" model="ir.actions.act_window">
          <field name="name">Managers Emails</field>
          <field name="res_model">ro.managers.approval</field>
          <field name="type">ir.actions.act_window</field>
          <field name="view_mode">tree,form</field>
          <field name="view_id" ref="managers_email_list"/>
          <field name="search_view_id" ref="view_managers_email_arc_post_search"/>
        </record>
        
        <menuitem action="managers_email_action_window" id="managers_email_menu_helpdesk_app" sequence="20" parent="helpdesk.helpdesk_menu_config"/>
    
        
    
    </data>
    
</odoo>