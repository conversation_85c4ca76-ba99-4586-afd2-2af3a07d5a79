from odoo import models, fields, api

class ProductTemplate(models.Model):

    _inherit = 'product.template'

    update_qty = fields.Boolean(default=False)


class SaleOrderLine(models.Model):

    _inherit = 'sale.order.line'

    tag_ids = fields.Many2many(
        comodel_name='sol.tag',
        string="Tags")
    
    
    @api.onchange('tag_ids')
    def _onchange_tag_ids(self):
        for line in self:
            if line.product_id.update_qty == True:
                line.product_uom_qty = len(line.tag_ids)
