from odoo import models, fields, api
from random import randint

class SolTag(models.Model):
    _name = "sol.tag"
    _description = "Sale Order Line Tag"

    def _get_default_color(self):
        return randint(1, 11)

    name = fields.Char('Tag Name', required=True, translate=True)
    color = fields.Integer('Color', default=_get_default_color)

    _sql_constraints = [
        ('name_uniq', 'unique (name)', "Tag name already exists !"),
    ]
