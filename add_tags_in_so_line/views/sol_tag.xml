<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--
            sol.tag views
        -->
        <record id="sales_line_sol_tag_view_form" model="ir.ui.view">
            <field name="name">sales.line.sol.tag.view.form</field>
            <field name="model">sol.tag</field>
            <field name="arch" type="xml">
                <form string="Tags">
                    <sheet>
                        <div class="oe_title">
                            <label for="name"/>
                            <h1>
                                <field name="name" placeholder="e.g. Services"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="color" required="True" widget="color_picker"/>
                            </group>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="sales_line_sol_tag_view_tree" model="ir.ui.view">
            <field name="name">sales.line.sol.tag.view.tree</field>
            <field name="model">sol.tag</field>
            <field name="arch" type="xml">
                <list string="Tags" editable="bottom" sample="1">
                    <field name="name"/>
                    <field name="color" widget="color_picker" />
                </list>
            </field>
        </record>

        <!-- Tags Configuration -->
        <record id="sales_line_sol_tag_action" model="ir.actions.act_window">
            <field name="name">Tags</field>
            <field name="res_model">sol.tag</field>
            <field name="view_id" ref="sales_line_sol_tag_view_tree"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                Create SOL Tags
                </p><p>
                Use Tags to manage and track your sale line (product structure, sales type, ...)
                </p>
            </field>
        </record>


        <menuitem id="menu_sol_tag_config"
                    name="SOL Tags"
                    action="sales_line_sol_tag_action"
                    sequence="10"
                    parent="sale.menu_sales_config"/>

    </data>
    
</odoo>
