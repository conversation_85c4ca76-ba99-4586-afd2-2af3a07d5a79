from odoo import api, models, fields, _
from odoo.exceptions import UserError
from odoo.addons.sale_stock.models.sale_order_line import SaleOrderLine


def _get_procurement_group(self):
    return False
#self.order_id.procurement_group_id
SaleOrderLine._get_procurement_group = _get_procurement_group

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'
    def _prepare_procurement_group_vals(self):
        res = super(SaleOrderLine, self)._prepare_procurement_group_vals()
        res['ro_sale_line_id'] = self.id
        return res
    

class ProcurementGroup(models.Model):
    _inherit = 'procurement.group'
    
    ro_sale_line_id = fields.Many2one('sale.order.line')
    
