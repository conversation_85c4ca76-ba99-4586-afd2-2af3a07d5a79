from odoo import fields, models,api,_


class LoanLines(models.Model):
    _name = "loan.lines"
    _description = "lines"

    name = fields.Char(string='name')
    loan_id = fields.Many2one('hr.employee.loan', string='Loan')
    ro_payment_date = fields.Date(string='Payment date')
    currency_id = fields.Many2one('res.currency', string='Currency')
    amount = fields.Monetary(string='Loan Amount', currency_field='currency_id')
    ro_paid = fields.Boolean(string='PAID')
    ro_payslip_ref = fields.Char(string='Payslip Ref')
    total_paid_amount = fields.Monetary(related='loan_id.total_paid_amount', string='Total Paid Amount', currency_field='currency_id', store=False)

   

    

