from odoo import fields, models, api, _


class contractInherited(models.Model):
    _inherit = 'hr.contract'
    
    currency_id = fields.Many2one('res.currency', string='Currency')
    loan_id = fields.Many2one('hr.employee.loan', string='Loan')
    # ro_loan_balance = fields.Monetary(related='employee_id.ro_loan_balance')

    
    
    
    ro_insurance_wage = fields.Monetary('Insurance wage')
    ro_goods_allowances = fields.Monetary('Goods Allowances')
    ro_transportation_allowances = fields.Monetary('Transportation Allowances')

