from odoo import fields, models, api, _


class HrEmployeeInherited(models.Model):
    _inherit = 'hr.employee'

    # ro_loan_balance = fields.Monetary(string='Reamining Loan Amount', compute='_compute_loan_balance')

   
    ## @api.depends('employee_id')
    # def _compute_loan_balance(self):
    #     for record in self:
    #         # #if record.employee_id:
    #             employee_loans = self.env['hr.employee.loan'].search([('ro_employee', '=', record.id) , ('state', '=', 'paid')])
    #             total_balance = sum(employee_loans.mapped('ro_balance'))
    #             record.ro_loan_balance = total_balance
    #         ## else:
    #             ## record.ro_loan_balance = 0.0


  
  