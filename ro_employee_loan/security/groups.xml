<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record  id="first_approve_group" model="res.groups">
            <field name="name">First Approve [R&amp;D]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
        <record  id="second_approve_group" model="res.groups">
            <field name="name">second Approve [R&amp;D]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
    </data>
</odoo>
