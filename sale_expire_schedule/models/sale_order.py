
from odoo import models, fields, api
from datetime import datetime,timedelta

class SaleOrder(models.Model):
   _inherit = "sale.order"


   def write(self, values):
      rec = super(SaleOrder, self).write(values)
      if values.get('validity_date') and values['validity_date']:
         group_notify = self.env.ref('sale_expire_schedule.group_manager_sale_expire')
         users = group_notify.users
         for user in users:
            data = {
                  'res_id': self.id,
                  'res_model_id': self.env.ref('sale.model_sale_order').id,
                  'user_id': user.id if user else False,
                  'summary': 'This Oreder has Expiration date on %s' %(values['validity_date']),
                  'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                  'date_deadline': self.validity_date-timedelta(days=5),
            }
            self.env['mail.activity'].create(data)

      return rec
