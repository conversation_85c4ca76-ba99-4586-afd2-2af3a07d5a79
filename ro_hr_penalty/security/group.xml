<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record  id="hr_penalty_group" model="res.groups">
            <field name="name">Show Penalty group[R&amp;D]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
         <record  id="penalty_confirm_group_group" model="res.groups">
            <field name="name">Confirm Penalty[R&amp;D]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
       
    </data>
</odoo>
