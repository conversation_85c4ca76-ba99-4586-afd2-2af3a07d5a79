from odoo import fields, Command, models, api, _


class HrPayslipInput(models.Model):
    _inherit = 'hr.payslip.input'
    
    penalty_ids = fields.Many2many('hr.penalty', string='Penalty')

    no_days = fields.Float("Days")

class Payslip(models.Model):
    _inherit = 'hr.payslip'

    ro_penalty_days = fields.Float('Penalty')
    ro_penalty_amount = fields.Float('Penalty')

    penalty_ids = fields.Many2many('hr.penalty', string='Penalty',compute='_compute_penalty',store=True)

    @api.depends('employee_id', 'date_from', 'date_to')
    def _compute_penalty(self):
        for payslip in self:
            if payslip.employee_id:
                penalties = self.env['hr.penalty'].search([
                    ('ro_penalty_employee', '=', payslip.employee_id.id),
                    ('ro_penalty_date', '>=', payslip.date_from),
                    ('ro_penalty_date', '<=', payslip.date_to),
                    ('state', '=', 'confirm'),
                ])
                penalties_days = penalties.filtered(lambda x: x.selection_type == 'days')
                penalties_amount = penalties.filtered(lambda x: x.selection_type == 'amount')
                
                payslip.penalty_ids = penalties.ids
                payslip.ro_penalty_days = sum(penalties_days.mapped('ro_days'))
                payslip.ro_penalty_amount = sum(penalties_amount.mapped('ro_amount'))
    
    def compute_sheet(self):
        for payslip in self:
            payslip._compute_penalty()
            lines_to_remove = payslip.input_line_ids.filtered(lambda x: x.penalty_ids or x.input_type_id.is_penalty)
            input_line_vals = [Command.unlink(line.id) for line in lines_to_remove]

            penalty_types = payslip.penalty_ids.mapped('ro_reason_type')
            for type in penalty_types:
                penalties_filtered = payslip.penalty_ids.filtered(lambda x:x.ro_reason_type == type)
                input_line_vals.append(Command.create({
                    'name': type.name,
                    'amount': -sum(penalties_filtered.mapped('ro_amount')),
                    'no_days': sum(penalties_filtered.mapped('ro_days')),
                    'input_type_id': type.id,
                    'penalty_ids': penalties_filtered.ids,
                }))
            payslip.update({'input_line_ids': input_line_vals})
        return super(Payslip, self).compute_sheet()
