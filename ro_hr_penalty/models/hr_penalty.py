from odoo import fields, models,api,_
from datetime import datetime, timedelta

class Penalty(models.Model):
    _name = "hr.penalty"
    _description = "Penalty"

    name = fields.Char(string='name')
    ro_penalty_employee = fields.Many2one('hr.employee', string='Employee',required=True)
    ro_penalty_date = fields.Date(string='Date', default=fields.Date.today)

    selection_type = fields.Selection([('days', 'Days'), ('amount', 'Amount')], string='Selection Type', required=True)
    ro_days = fields.Float(string='Days')
    ro_amount = fields.Float(string='Amount')
    ro_reason_type = fields.Many2one('hr.payslip.input.type', string='Reason Type', domain=[('is_penalty','=',True)])
    ro_note = fields.Char(string='Note')
     
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirm', ' Confirmed'),   
    ], required=True, default='draft', copy=False, tracking=True)  

    @api.model
    def create(self, vals):
        vals['name'] = self.env['ir.sequence'].next_by_code('penalty_seq')
        return super(Penalty, self).create(vals)

    def action_confirm(self):
        self.state = 'confirm'
        # self.write({'state': 'confirm'})    
             
           
       
    
    
