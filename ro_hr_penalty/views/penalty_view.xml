<?xml version="1.0" encoding="utf-8"?>

<odoo>
  <data>

    <record id="hr_employee_Penalty_tree_view" model="ir.ui.view">
        <field name="name">hr.penalty.tree</field>
        <field name="model">hr.penalty</field>
        <field name="arch" type="xml">
          <tree string="Hr Penalty">
            <field name="name"/>
            <field name="ro_penalty_employee"/>
            <field name="ro_penalty_date"/>

            <field name="state" widget="badge" select="1" readonly="1"/>
              
          </tree>
        </field>
    </record>

    <record id="hr_Penalty_form_view" model="ir.ui.view">
      <field name="name">hr.penalty.form</field>
      <field name="model">hr.penalty</field>
      <field name="arch" type="xml">
        <form string="Hr Penalty">
          <header>
            <button name="action_confirm" string="Confirm" type="object" groups="ro_hr_penalty.penalty_confirm_group_group"  attrs="{'invisible': [('state', '=', 'confirm')]}"/>
            <field name="state" widget="statusbar" select="1" readonly="1"/>
          </header>               
          <sheet>           
            <div class="oe_title">
              <h1>
                <field name="name" readonly="1"/>
              </h1>        
            </div>
            <group> 
              <group name="penalty">
                  <!-- <field name="ro_employee" attrs="{'readonly': [('state', '=', 'second_approve')]}" /> -->
                  <field name="ro_penalty_employee"  attrs="{'readonly': [('state', '=', 'confirm')]}" options='{"no_open": True, "no_create": True}'/>
                  <field name="selection_type" attrs="{'readonly': [('state', '=', 'confirm')]}"/>
                  <field name="ro_days"  attrs="{'readonly': [('state', '=', 'confirm')],'invisible': [('selection_type', '!=', 'days')]}"/>
                  <field name="ro_amount"  attrs="{'readonly': [('state', '=', 'confirm')],'invisible': [('selection_type', '!=', 'amount')]}" />
              </group>
              <group name="penalty_details">
                  <field name="ro_penalty_date" attrs="{'readonly': [('state', '=', 'confirm')]}"/>
                  <field name="ro_reason_type" attrs="{'readonly': [('state', '=', 'confirm')]}" options='{"no_open": True}' />
                  <field name="ro_note" attrs="{'readonly': [('state', '=', 'confirm')]}"/>
              </group>
                        
            </group>
          </sheet>
        </form>
      </field>
    </record>
                      
    <record id="hr_employee_Penalty_action" model="ir.actions.act_window">
      <field name="name">Penalty</field>
      <field name="res_model">hr.penalty</field>
      <field name="view_mode">tree,form</field>
      <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
              Create a new Penalty
          </p>
      </field>
    </record>

    <menuitem
            id="menu_hr_penalty"
            name="Penalty"
            action="hr_employee_Penalty_action"
            parent="hr_work_entry_contract_enterprise.menu_hr_payroll_root"
            sequence="121"
            groups="ro_hr_penalty.hr_penalty_group"/>  

  </data>
</odoo>
