<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_hr_payslip_input_type_form_inherit_penalty" model="ir.ui.view">
        <field name="name">hr.payslip.input.type.view.inherit</field>
        <field name="model">hr.payslip.input.type</field>
        <field name="inherit_id" ref="hr_payroll.hr_payslip_input_type_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='code']" position="after">
                <field name="is_penalty" /> 
            </xpath>
        </field>
    </record> 

    <record id="view_hr_payslip_form_inherit_add_penalty" model="ir.ui.view">
        <field name="name">hr.payslip.view.inherit</field>
        <field name="model">hr.payslip</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='struct_id']" position="after">
                <field name="ro_penalty_days" invisible="1"/> 
                <field name="ro_penalty_amount" invisible="1"/> 

                <!-- <field name="penalty_ids" invisible="1" />  -->
                <field name="penalty_ids"  widget="many2many_tags" invisible="1"/> 
            </xpath>

            <xpath expr="//field[@name='input_line_ids']/tree/field[@name='amount']" position="before">
                <field name="no_days" /> 
            </xpath>
        </field>
    </record> 

</odoo>
