# -*- coding: utf-8 -*-

from odoo import fields, models


class AccountInvoiceReport(models.Model):
    _inherit = 'account.invoice.report'

    pricelist_id = fields.Many2one('product.pricelist', string='Pricelist', readonly=True)


    def _select(self):
        return super(AccountInvoiceReport, self)._select() + ", move.pricelist_id as pricelist_id"


    def _group_by(self):
        return super(AccountInvoiceReport, self)._group_by() + ", move.pricelist_id"
