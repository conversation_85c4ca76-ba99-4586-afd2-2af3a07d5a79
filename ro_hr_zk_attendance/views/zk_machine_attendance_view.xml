<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Customize Attendance Report -->
    <record id="ro_inherited_view_attendance_tree" model="ir.ui.view">
        <field name="name">inherited_hr.attendance.tree</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.view_attendance_tree" />
        <field name="arch" type="xml">
            <tree string="Employee attendances">
                <field name="worked_hours" type="measure" />
            </tree>
        </field>
    </record>

    <record id="ro_inherited_hr_attendance_view_filter" model="ir.ui.view">
        <field name="name">inherited_hr_attendance_view_filter</field>
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_filter" />
        <field name="arch" type="xml">
            <search string="Hr Attendance Search">
                <group expand="0" string="Group By">
                    <separator />
                    <filter name="date" string="Date" context="{'group_by':'check_in:day'}" />

                </group>
            </search>
        </field>
    </record>

    <record id="ro_view_zk_machine_attendance_search" model="ir.ui.view">
        <field name="name">zk.machine.attendance.search</field>
        <field name="model">zk.machine.attendance</field>
        <field name="arch" type="xml">
            <search string="Hr Attendance Search">
                <filter name="today" string="Today"
                    domain="[('punching_time', '&gt;=', datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')),('punching_time', '&lt;=',datetime.datetime.now().strftime('%Y-%m-%d 23:23:59'))]" />
                <filter name="yesterday" string="Yesterday"
                    domain="[('punching_time', '&gt;=', (datetime.datetime.now()- datetime.timedelta(1)).strftime('%Y-%m-%d 00:00:00')),('punching_time', '&lt;=',(datetime.datetime.now()- datetime.timedelta(1)).strftime('%Y-%m-%d 23:23:59'))]" />
                <filter string="Current Month" name="month"
                    domain="[('punching_time', '&gt;=', datetime.datetime.now().strftime('%Y-%m-01'))]" />
                <separator />

                <field name="name" string="Name" />
            </search>
        </field>
    </record>

    <record id="ro_view_zk_machine_attendance_tree" model="ir.ui.view">
        <field name="name">zk.machine.attendance.tree</field>
        <field name="model">zk.machine.attendance</field>
        <field name="arch" type="xml">
            <!--            <tree string="Attendance" create="false" delete="false" colors="green:punch_type in
            ('0');red:punch_type in ('1');">-->
            <tree editable="top">
                <field name="name" />
                <field name="punch_type" />
                <field name="attendance_type" />
                <field name="punching_time" />
                <field name="address_id" optional="hide" />
                <field name="added" />
                <field name="create_date" optional="hide" />
            </tree>
        </field>
    </record>

    <record id="ro_action_zk_machine_attendance" model="ir.actions.act_window">
        <field name="name">Attendance</field>
        <field name="res_model">zk.machine.attendance</field>
        <field name="view_mode">tree</field>
        <field name="search_view_id" ref="ro_view_zk_machine_attendance_search" />
    </record>

    <record id="ro_update_attendance_log_action" model="ir.actions.server">
        <field name="name">Update Attendance Log</field>
        <field name="model_id" ref="model_zk_machine_attendance" />
        <field name="binding_model_id" ref="model_zk_machine_attendance" />
        <field name="state">code</field>
        <field name="code">
            if records:
            action = records.update_attendance_log()
        </field>
    </record>

    <record id="ro_add_action" model="ir.actions.server">
        <field name="name">Add Attendance</field>
        <field name="model_id" ref="model_zk_machine_attendance" />
        <field name="binding_model_id" ref="model_zk_machine_attendance" />
        <field name="state">code</field>
        <field name="code">
            if records:
            action = records.add_attendance()
        </field>
    </record>

    <!--odoo17:
    hr_attendance.group_hr_attendance_officer-->
    <!--odoo16:
    hr_attendance.group_hr_attendance_user-->
    <menuitem id="menu_zk_attendance_view" name="Attendance log"
        action="ro_action_zk_machine_attendance" parent="zk_machine_menu"
        sequence="2" groups="hr_attendance.group_hr_attendance_user" />
</odoo>