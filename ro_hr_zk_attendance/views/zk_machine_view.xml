<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_zk_machine_form" model="ir.ui.view">
        <field name="name">zk.machine.form</field>
        <field name="model">zk.machine</field>
        <field name="arch" type="xml">
            <form string="Biometric Device">
                <header>
                    <!--odoo16:
                    attrs="{'invisible': [('machine_type','=','local')]}"-->
                    <!--odoo17:
                    invisible="machine_type == 'local'"-->
                    <button name="download_attendance_ip" type="object" string="Download Data"
                        class="oe_highlight"
                        icon="fa-download " confirm="Are you sure you want to do this?"
                        attrs="{'invisible': [('machine_type','=','local')]}" />
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" />
                        <h1>
                            <field name="name" placeholder="Machine IP" />
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="port_no" />
                            <field name="machine_number" />
                            <field name="comm_pass" />
                            <field name="machine_type" />
                            <field name="address_id" />
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" />
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_zk_machine_tree" model="ir.ui.view">
        <field name="name">zk.machine.tree</field>
        <field name="model">zk.machine</field>
        <field name="arch" type="xml">
            <tree string="Biometric Machine">
                <field name="active" invisible="1" />
                <field name="name" />
                <field name="port_no" />
                <field name="machine_number" />
                <field name="comm_pass" />
                <field name="machine_type" />
                <field name="address_id" optional="hide" />
                <field name="company_id" groups="base.group_multi_company" />
            </tree>
        </field>
    </record>

    <record id="view_zk_machine_search" model="ir.ui.view">
        <field name="model">zk.machine</field>
        <field name="arch" type="xml">
            <search>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]" />
            </search>
        </field>
    </record>

    <record id="zk_machine_action" model="ir.actions.act_window">
        <field name="name">Attendances</field>
        <field name="res_model">zk.machine</field>
        <field name="view_mode">tree,form</field>
    </record>

    <record id="hr_employee_inherit_form_view" model="ir.ui.view">
        <field name="name">hr.employee.form</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form" />
        <field name="arch" type="xml">
            <xpath expr="//page[@name='hr_settings']//field[@name='user_id']" position="after">
                <field name="ro_device_id" />
            </xpath>
        </field>
    </record>

    <record id="ro_hr_attendance_view_filter_device" model="ir.ui.view">
        <field name="model">hr.attendance</field>
        <field name="inherit_id" ref="hr_attendance.hr_attendance_view_filter" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='employee_id']" position="before">
                <field name="ro_device_id" />
            </xpath>
        </field>
    </record>

    <menuitem id="zk_machine_menu" parent="hr_attendance.menu_hr_attendance_root" sequence="50"
        name="Biometric Manager" />
    <menuitem id="zk_machine_sub_menu" parent="zk_machine_menu" name="Device Configuration"
        action="zk_machine_action" sequence="1" />
</odoo>