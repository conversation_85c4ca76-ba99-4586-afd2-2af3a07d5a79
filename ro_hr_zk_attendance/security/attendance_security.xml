<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Multi - Company Rules -->
        <record id="ro_machine_comp_rule" model="ir.rule">
            <field name="name">Machine multi-company</field>
            <field name="model_id" ref="model_zk_machine"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>

        <record id="ro_machine_attendance_comp_rule" model="ir.rule">
            <field name="name">Machine Attendance multi-company</field>
            <field name="model_id" ref="model_zk_machine_attendance"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>