# -*- coding: utf-8 -*-

from odoo import models, fields, api


# class Sale<PERSON>rder(models.Model):
#     _inherit = 'sale.order'

#     @api.onchange('team_id')
#     def _set_analytic_account(self):
#         for rec in self:
#             if rec.team_id.analytic_account_id:
#                 rec.analytic_account_id = rec.team_id.analytic_account_id

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    @api.depends('order_id.partner_id', 'product_id', 'order_id.team_id')
    def _compute_analytic_distribution(self):
        for line in self:
            if not line.display_type and line.state == 'draft':
                distribution = line.env['account.analytic.distribution.model']._get_distribution({
                    "product_id": line.product_id.id,
                    "product_categ_id": line.product_id.categ_id.id,
                    "partner_id": line.order_id.partner_id.id,
                    "partner_category_id": line.order_id.partner_id.category_id.ids,
                    "company_id": line.company_id.id,
                    "team_id":line.order_id.team_id.id,
                })
                line.analytic_distribution = distribution or line.analytic_distribution