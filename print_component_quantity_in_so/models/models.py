# -*- coding: utf-8 -*-

from odoo import models, fields, api


class CrmLead(models.Model):
    _inherit = 'crm.lead'


class CrmLeadLine(models.Model):
    _inherit = 'crm.lead.line'

    def component_need_prepare(self, product_id):
        component = {}
        bill_of_materials = product_id.variant_bom_ids or product_id.bom_ids
        if bill_of_materials:
            for bom in bill_of_materials.bom_line_ids:
                bill_of_materials_second_level = bom.product_id.variant_bom_ids or bom.product_id.bom_ids
                # if there is second level so need to check second level else check only first level
                if bill_of_materials_second_level:
                    # component.append(bill_of_materials_second_level.bom_line_ids) 
                    for line in bill_of_materials_second_level.bom_line_ids:
                        
                        component[line.product_id.id] = {
                            'product_id':line.product_id.name,
                            'product_qty': line.product_qty * bom.product_qty,
                            'product_uom_id':line.product_uom_id.name,
                        }
                else:
                    # component.append(bom) 
                    component[bom.product_id.id] = {
                            'product_id':bom.product_id.name,
                            'product_qty': bom.product_qty,
                            'product_uom_id':bom.product_uom_id.name,
                        }
        return component
