<odoo>
    <data>
      <record id="action_component_need_preparation_print" model="ir.actions.report">
        <field name="name">Component need preparation</field>
        <field name="model">crm.lead</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">print_component_quantity_in_so.prepare_component_print</field>
        <field name="report_file">print_component_quantity_in_so.prepare_component_print</field>
        <!-- <field name="paperformat_id" ref="print_component_quantity_in_so.paperformat_portrait_component"/> -->
        <field name="binding_model_id" ref="crm.model_crm_lead"/>
        <field name="binding_type">report</field>
      </record>
  
  
       
  
  
      <template id="prepare_component_print">
        <t t-call="web.html_container">
          <t t-foreach="docs" t-as="doc">

            <t t-call="web.external_layout">

                
              <div class="page">
                <div class="row">
                    <div style="width:100%">
                        <center>
                            <h2 style = "text-align:center">Component Need Preparation</h2>
                        </center>
                    </div>
                </div>

                <br/>

                <table class="table table-bordered">
                    <!-- In case we want to repeat the header, remove "display: table-row-group" -->
                    <thead style="display: table-row-group">
                        <tr>
                            <th name="th_description" class="text-left">Product</th>
                            <th name="th_quantity" class="text-right">Quantity</th>
                            
                        </tr>
                    </thead>
                    <tbody class="sale_tbody">


                        <t t-foreach="doc.crm_line" t-as="line">
                          <tr>
                            <td name="td_name" colspan="2" style="background-color:#C0C0C0;"><span t-esc="line.product_id.name"/></td>
                          </tr>
                            <t t-set="components" t-value="request.env['crm.lead.line'].sudo().component_need_prepare(line.product_id)"/>

                            <t t-if="components">
                              <t t-foreach="components" t-as="com">
                                <tr>
                                  <t t-if="not line.display_type">
                                      <!-- <td name="td_name"><span t-esc="com.product_id[0].name"/></td>
                                      <td name="td_quantity" class="text-right">
                                          <span t-esc="line.product_uom_qty * com.product_qty"/>
                                          <span t-field="line.product_uom"/>
                                      </td> -->
                                      <td name="td_name"><span t-esc="components[com]['product_id']"/></td>
                                      <td name="td_quantity" class="text-right">
                                          <span t-esc="line.product_uom_qty * components[com]['product_qty']"/>
                                          <span t-field="line.product_uom"/>
                                      </td>
                                      
                                  </t>
                                </tr>
  
                              </t>

                            </t>
                            
                        </t>
                    </tbody>
                </table>

              </div>
            </t>
          </t>
        </t>
      </template>
    </data>
  </odoo>