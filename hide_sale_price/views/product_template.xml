<odoo>
  <data>
      <record id="hide_sales_price_inherit_product_template_form_view" model="ir.ui.view">
          <field name="model">product.template</field>
          <field name="inherit_id" ref="product.product_template_form_view"/>
          <field name="arch" type="xml">
              <xpath expr="//label[@for='list_price']" position="attributes">    
                  <attribute name="groups">hide_sale_price.group_manager_sales_price</attribute>
              </xpath>

              <!-- <xpath expr="//div[@name='pricing']" position="attributes">    
                  <attribute name="groups">hide_sale_price.group_manager_sales_price</attribute>
              </xpath> -->
          </field>
      </record>

      <record id="hide_sales_price_inherit_product_template_tree_view" model="ir.ui.view">
          <field name="model">product.template</field>
          <field name="inherit_id" ref="product.product_template_tree_view"/>
          <field name="arch" type="xml">
              <xpath expr="//field[@name='list_price']" position="attributes">    
                  <attribute name="groups">hide_sale_price.group_manager_sales_price</attribute>
              </xpath>
          </field>
      </record>

      <record id="hide_sales_price_inherit_product_template_kanban_view" model="ir.ui.view">
          <field name="model">product.template</field>
          <field name="inherit_id" ref="product.product_template_kanban_view"/>
          <field name="arch" type="xml">
              <!-- <xpath expr="//div[@name='product_lst_price']" position="attributes">    
                  <attribute name="groups">hide_sale_price.group_manager_sales_price</attribute>
              </xpath> -->
          </field>
      </record>

  </data>
</odoo> 