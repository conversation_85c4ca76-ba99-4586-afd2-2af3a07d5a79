<odoo>
    <data>
        <!-- sale order form price  -->
        <record id="sale_order_price_inherit" model="ir.ui.view">
            <field name="name">sale.order.price.edit.inheritd</field>
            <field name="model">sale.order</field>
            <field name="priority">51</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//form//field[@name='price_unit']" position="before">
                    <field name='is_sale_manager' invisible="1"/>
                </xpath>
                <xpath expr="//list//field[@name='price_unit']" position="before">
                    <field name='is_sale_manager' invisible="1"/>
                </xpath>
                <xpath expr="//kanban//field[@name='price_unit']" position="before">
                    <field name='is_sale_manager' invisible="1"/>
                </xpath>
                <xpath expr="//form//field[@name='price_unit']" position="attributes">
                    <attribute name="attrs">{'readonly':[('is_sale_manager','=',False)]}</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//list//field[@name='price_unit']" position="attributes">
                    <attribute name="attrs">{'readonly':['|', ('is_sale_manager','=',False), ('qty_invoiced', '&gt;', 0)]}</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//kanban//field[@name='price_unit']" position="attributes">
                    <attribute name="attrs">{'readonly':[('is_sale_manager','=',False)]}</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
            </field>
        </record>
        <!-- sale order form price  -->
        <record id="invoice_customer_inherit" model="ir.ui.view">
            <field name="name">customer.invoice.price.edit.inheritd</field>
            <field name="model">account.move</field>
            <field name="priority">51</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//list//field[@name='price_unit']" position="before">
                    <field name='is_manager_user' invisible="1"/>
                </xpath>
                <xpath expr="//kanban//field[@name='price_unit']" position="before">
                    <field name='is_manager_user' invisible="1"/>
                </xpath>
                <xpath expr="//list//field[@name='price_unit']" position="attributes">
                    <attribute name="attrs">{'readonly':[('is_manager_user','=',False)]}</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//kanban//field[@name='price_unit']" position="attributes">
                    <attribute name="attrs">{'readonly':[('is_manager_user','=',False)]}</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
                <xpath expr="//page[@id='aml_tab']" position="attributes">
                    <attribute name="groups">account.group_account_manager</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>