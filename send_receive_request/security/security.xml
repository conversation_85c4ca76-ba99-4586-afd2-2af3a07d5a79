<odoo>
    
    <record  id="group_cash_in_out" model="res.groups">
        <field name="name">Cash in/out group [ShinyWhite]</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>
    <record  id="group_petty_cash" model="res.groups">
        <field name="name">Petty Cash group [ShinyWhite]</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

</odoo>