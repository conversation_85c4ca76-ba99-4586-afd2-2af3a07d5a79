import logging

from odoo import models, fields, api,_
from odoo import tools
from datetime import datetime
from num2words import num2words


from odoo.exceptions import UserError, ValidationError


class AccountPayment(models.Model):
        _inherit="account.payment"

        is_document = fields.Boolean()

        partner_id = fields.Many2one(
                comodel_name='res.partner',
                string="Customer/Vendor",
                store=True, readonly=False, ondelete='restrict',
                # compute='_compute_partner_id',
                # domain="[('user_id','=',uid)]",
                # '|', ('parent_id','=', False), ('is_company','=', True)
                tracking=True,
                check_company=True)
        destination_account_id = fields.Many2one(
                comodel_name='account.account',
                string='Destination Account',
                store=True, readonly=False,
                compute='_compute_destination_account_id',
                # ('user_type_id.type', 'in', ('receivable', 'payable')),
                domain="[('company_id', '=', company_id)]",
                check_company=True)
    
        # plan_id = fields.Many2one('account.analytic.plan',string='Project')

        @api.onchange('payment_type')
        def compute_flag(self):
            for this in self:
                if this.payment_type == 'inbound':
                    this.journal = 'in'
                elif this.payment_type == 'outbound':
                    this.journal = 'out'

        journal = fields.Char(compute='compute_flag')

        # @api.onchange('payment_type')
        # def _check_on_post_type(self):
        #         for this in self:
        #             this.post_type = 'single'
                #     this.journal_id =
                                # if any(line.partner_id != all_lines[0].partner_id for line in all_lines):
                                #         raise UserError(_(
                                #                 "The journal entry %s reached an invalid state relative to its payment.\n"
                                #                 "To be consistent, the journal items must share the same partner."
                                # ) % move.display_name) False


        # @api.depends('is_internal_transfer')
        # def _compute_destination_account_id(self):
        #         # self.destination_account_id = False
        #         for pay in self:
        #                 if pay.is_internal_transfer:
        #                         pay.destination_account_id = pay.journal_id.company_id.transfer_account_id
        #                 elif pay.partner_type == 'customer' and not pay.is_document:
        #                         # Receive money from invoice or send money to refund it.
        #                         if pay.partner_id:
        #                                 pay.destination_account_id = pay.partner_id.with_company(pay.company_id).property_account_receivable_id
        #                         else:
        #                                 pay.destination_account_id = self.env['account.account'].search([
        #                                         ('company_id', '=', pay.company_id.id),
        #                                         ('internal_type', '=', 'receivable'),
        #                                 ], limit=1)
        #                 elif pay.partner_type == 'supplier' and not pay.is_document:
        #                         # Send money to pay a bill or receive money to refund it.
        #                         if pay.partner_id:
        #                                 pay.destination_account_id = pay.partner_id.with_company(pay.company_id).property_account_payable_id
        #                         else:
        #                                 pay.destination_account_id = self.env['account.account'].search([
        #                                         ('company_id', '=', pay.company_id.id),
        #                                         ('internal_type', '=', 'payable'),
        #                                 ], limit=1)


        post_type = fields.Selection([('single', 'Single Account'), ('multi', 'Multiple Accounts')], default='single',
                                     string='Post Difference In To')

        writeoff_multi_acc_ids = fields.One2many('writeoff.accounts', 'payment_id', string='Write Off Accounts')

        numinwords = fields.Char(string='Total in words',compute='_getnumbernew',store=False)


        @api.depends('amount')
        def _getnumbernew(self):
                for rec in self:

                        numinwords = num2words(float(rec.amount), lang='ar',)
                        numinwords = ('مبلغ وقدره {} جنيه مصري فقط لاغير').format(numinwords)

                        rec.numinwords = numinwords

        @api.onchange('date')
        def _compute_company_date(self):
                self.company_id = self.env.company.id

        # @api.onchange('post_type')
        # def _compute_empty_accounts(self):
        #         if self.post_type == 'multi':
        #                 self.writeoff_multi_acc_ids = [5,0,0]

        #         if self.post_type == 'multi':
        #                 self.destination_account_id = False
        @api.onchange('writeoff_multi_acc_ids')
        def _compute_multi_account(self):
                total = 0
                if self.writeoff_multi_acc_ids:
                        for line in self.writeoff_multi_acc_ids:
                                total += line.amount
                self.amount = total



        def _prepare_move_line_default_vals(self, write_off_line_vals=None):
                ''' Prepare the dictionary to create the default account.move.lines for the current payment.
                :param write_off_line_vals: Optional dictionary to create a write-off account.move.line easily containing:
                * amount:       The amount to be added to the counterpart amount.
                * name:         The label to set on the line.
                * account_id:   The account on which create the write-off.
                :return: A list of python dictionary to be passed to the account.move.line's 'create' method.
                '''
                self.ensure_one()
                if self.payment_method_code not in  ('received_third_check','received_safty_check','issue_check','delivered_third_check'):
                        write_off_line_vals = write_off_line_vals or {}
                        
                        if not self.outstanding_account_id:
                                raise UserError(_(
                                        "You can't create a new payment without an outstanding payments/receipts account set either on the company or the %s payment method in the %s journal.",
                                        self.payment_method_line_id.name, self.journal_id.display_name))

                        # Compute amounts.
                        write_off_line_vals_list = write_off_line_vals or []
                        write_off_amount_currency = sum(x['amount_currency'] for x in write_off_line_vals_list)
                        write_off_balance = sum(x['balance'] for x in write_off_line_vals_list)

                        if self.payment_type == 'inbound':
                                # Receive money.
                                liquidity_amount_currency = self.amount
                        elif self.payment_type == 'outbound':
                                # Send money.
                                liquidity_amount_currency = -self.amount
                        else:
                                liquidity_amount_currency = 0.0

                        liquidity_balance = self.currency_id._convert(
                                liquidity_amount_currency,
                                self.company_id.currency_id,
                                self.company_id,
                                self.date,
                        )
                        counterpart_amount_currency = -liquidity_amount_currency - write_off_amount_currency
                        counterpart_balance = -liquidity_balance - write_off_balance
                        currency_id = self.currency_id.id

                        # Compute a default label to set on the journal items.
                        liquidity_line_name = ''.join(x[1] for x in self._get_liquidity_aml_display_name_list())
                        counterpart_line_name = ''.join(x[1] for x in self._get_counterpart_aml_display_name_list())

                        if self.post_type == 'single':

                                line_vals_list = [
                                        # Liquidity line.
                                        {
                                        'name': liquidity_line_name,
                                        'date_maturity': self.date,
                                        'amount_currency': liquidity_amount_currency,
                                        'currency_id': currency_id,
                                        'debit': liquidity_balance if liquidity_balance > 0.0 else 0.0,
                                        'credit': -liquidity_balance if liquidity_balance < 0.0 else 0.0,
                                        'partner_id': self.partner_id.id,
                                        'account_id': self.outstanding_account_id.id,
                                        },
                                        # Receivable / Payable.
                                        {
                                        'name': counterpart_line_name,
                                        'date_maturity': self.date,
                                        'amount_currency': counterpart_amount_currency,
                                        'currency_id': currency_id,
                                        'debit': counterpart_balance if counterpart_balance > 0.0 else 0.0,
                                        'credit': -counterpart_balance if counterpart_balance < 0.0 else 0.0,
                                        'partner_id': self.partner_id.id,
                                        'account_id': self.destination_account_id.id,
                                        },
                                ]
                        elif self.post_type == 'multi':
                                
                                line_vals_list = [
                                        # Liquidity line.
                                        {
                                        'name': liquidity_line_name,
                                        'date_maturity': self.date,
                                        'amount_currency': liquidity_amount_currency,
                                        'currency_id': currency_id,
                                        'debit': liquidity_balance if liquidity_balance > 0.0 else 0.0,
                                        'credit': -liquidity_balance if liquidity_balance < 0.0 else 0.0,
                                        'partner_id': self.partner_id.id,
                                        'account_id': self.outstanding_account_id.id,
                                        }]
                                for account_line in self.writeoff_multi_acc_ids:
                                        
                                        line_vals_list.append(
                                        # Receivable / Payable.
                                        {
                                        'name': account_line.name or counterpart_line_name,
                                        'date_maturity': self.date,
                                        'amount_currency': account_line.amount,
                                        'currency_id': currency_id,
                                        'debit': account_line.amount if liquidity_balance < 0.0 else 0.0,
                                        'credit': -account_line.amount if liquidity_balance > 0.0 else 0.0,
                                        'partner_id': account_line.writeoff_partner_id.id,
                                        'account_id': account_line.writeoff_account_id.id,
                                        'analytic_distribution': account_line.analytic_distribution,
                                        #'analytic_account_id': account_line.analytic_account_id.id,

                                        # 'analytic_account_id': account_line.analytic_account_id.id,
                                        # 'analytic_tag_ids': [(4, tag, None) for tag in account_line.analytic_tag_ids.ids],
                                })
        
                        return line_vals_list + write_off_line_vals_list
                else:
                        write_off_line_vals = write_off_line_vals or {}

                        if not self.outstanding_account_id:
                                raise UserError(_(
                                        "You can't create a new payment without an outstanding payments/receipts account set on the %s journal.",
                                        self.journal_id.display_name))

                        # Compute amounts.
                        write_off_amount_currency = write_off_line_vals.get('amount', 0.0)

                        if self.payment_type == 'inbound':
                                # Receive money.
                                liquidity_amount_currency = self.amount
                        elif self.payment_type == 'outbound':
                                # Send money.
                                liquidity_amount_currency = -self.amount
                                write_off_amount_currency *= -1
                        else:
                                liquidity_amount_currency = write_off_amount_currency = 0.0

                        write_off_balance = self.currency_id._convert(
                                write_off_amount_currency,
                                self.company_id.currency_id,
                                self.company_id,
                                self.date,
                        )
                        liquidity_balance = self.currency_id._convert(
                                liquidity_amount_currency,
                                self.company_id.currency_id,
                                self.company_id,
                                self.date,
                        )

                        # new
                        liquidity_account = False
                        if self.payment_type in ('outbound', 'transfer'):
                                if self.payment_method_code == 'issue_check':
                                        liquidity_account = self.journal_id.deferred_check_account_id.id
                                else:
                                        liquidity_account = self.outstanding_account_id.id
                        else:
                                if self.payment_method_code in ('received_third_check', 'received_safty_check'):
                                        liquidity_account = self.journal_id.holding_check_account_id.id
                                # elif self.payment_method_code == 'received_safty_check':
                                #     liquidity_account = self.journal_id.safty_check_account_id.id
                                else:
                                        liquidity_account = self.outstanding_account_id.id
                        # new
                

                        counterpart_amount_currency = -liquidity_amount_currency - write_off_amount_currency
                        counterpart_balance = -liquidity_balance - write_off_balance
                        currency_id = self.currency_id.id

                        # if self.is_internal_transfer:
                        #     if self.payment_type == 'inbound':
                        #         liquidity_line_name = _('Transfer to %s', self.journal_id.name)
                        #     else: # payment.payment_type == 'outbound':
                        #         liquidity_line_name = _('Transfer from %s', self.journal_id.name)
                        # else:
                        #     liquidity_line_name = self.payment_reference

                        # Compute a default label to set on the journal items.
                        liquidity_line_name = ''.join(x[1] for x in self._get_liquidity_aml_display_name_list())
                        counterpart_line_name = ''.join(x[1] for x in self._get_counterpart_aml_display_name_list())

                        line_vals_list = [
                                # Liquidity line.
                                {
                                        'name': liquidity_line_name,
                                        'date_maturity': self.date,
                                        'amount_currency': liquidity_amount_currency,
                                        'currency_id': currency_id,
                                        'debit': liquidity_balance if liquidity_balance > 0.0 else 0.0,
                                        'credit': -liquidity_balance if liquidity_balance < 0.0 else 0.0,
                                        'partner_id': self.partner_id.id,
                                        'account_id': liquidity_account,
                                },
                                # Receivable / Payable.
                                {
                                        'name': counterpart_line_name or self.payment_reference,
                                        'date_maturity': self.date,
                                        'amount_currency': counterpart_amount_currency,
                                        'currency_id': currency_id,
                                        'debit': counterpart_balance if counterpart_balance > 0.0 else 0.0,
                                        'credit': -counterpart_balance if counterpart_balance < 0.0 else 0.0,
                                        'partner_id': self.partner_id.id,
                                        'account_id': self.destination_account_id.id,
                                },
                                ]
                        if not self.currency_id.is_zero(write_off_amount_currency):
                                # Write-off line.
                                line_vals_list.append({
                                        'name': write_off_line_vals.get('name'),
                                        'amount_currency': write_off_amount_currency,
                                        'currency_id': currency_id,
                                        'debit': write_off_balance if write_off_balance > 0.0 else 0.0,
                                        'credit': -write_off_balance if write_off_balance < 0.0 else 0.0,
                                        'partner_id': self.partner_id.id,
                                        'account_id': write_off_line_vals.get('account_id'),
                                })
                        return line_vals_list

                
        def _synchronize_from_moves(self, changed_fields):
                ''' Update the account.payment regarding its related account.move.
                Also, check both models are still consistent.
                :param changed_fields: A set containing all modified fields on account.move.
                '''
                if self._context.get('skip_account_move_synchronization'):
                        return

                for pay in self.with_context(skip_account_move_synchronization=True):
                        # After the migration to 14.0, the journal entry could be shared between the account.payment and the
                        # account.bank.statement.line. In that case, the synchronization will only be made with the statement line.
                        if pay.move_id.statement_line_id:
                                continue

                        move = pay.move_id
                        move_vals_to_write = {}
                        payment_vals_to_write = {}
                        if 'journal_id' in changed_fields:
                                pay.with_context(skip_account_move_synchronization=False)
                                pay._synchronize_to_moves({'journal_id'})

                                if pay.journal_id.type not in ('bank', 'cash'):
                                        raise UserError(_("A payment must always belongs to a bank or cash journal."))

                        if 'line_ids' in changed_fields:
                                all_lines = move.line_ids
                                liquidity_lines, counterpart_lines, writeoff_lines = pay._seek_for_lines()
                                
                                # if writeoff_lines and len(writeoff_lines.account_id) != 1:
                                #         raise UserError(_(
                                #         "Journal Entry %s is not valid. In order to proceed, "
                                #         "all optional journal items must share the same account.",
                                #         move.display_name,
                                #         ))
                                # if len(liquidity_lines) != 1:
                                #         raise UserError(_(
                                #                 "Journal Entry %s is not valid. In order to proceed, the journal items must "
                                #                 "include one and only one outstanding payments/receipts account.",
                                #                 move.display_name,
                                #         ))

                                # if len(counterpart_lines) != 1:
                                #         raise UserError(_(
                                #                 "Journal Entry %s is not valid. In order to proceed, the journal items must "
                                #                 "include one and only one receivable/payable account (with an exception of "
                                #                 "internal transfers).",
                                #                 move.display_name,
                                #         ))
                                
                                if any(line.currency_id != all_lines[0].currency_id for line in all_lines):
                                        raise UserError(_(
                                                "Journal Entry %s is not valid. In order to proceed, the journal items must "
                                                "share the same currency.",
                                                move.display_name,
                                        ))

                                # if any(line.partner_id != all_lines[0].partner_id for line in all_lines):
                                #         raise UserError(_(
                                #                 "The journal entry %s reached an invalid state relative to its payment.\n"
                                #                 "To be consistent, the journal items must share the same partner."
                                # ) % move.display_name)

                                if counterpart_lines.account_id.account_type == 'asset_receivable':
                                        partner_type = 'customer'
                                else:
                                        partner_type = 'supplier'

                                liquidity_amount = liquidity_lines.amount_currency

                                move_vals_to_write.update({
                                        'currency_id': liquidity_lines.currency_id.id,
                                        'partner_id': liquidity_lines.partner_id.id,
                                })
                                payment_vals_to_write.update({
                                        'amount': abs(liquidity_amount),
                                        'partner_type': partner_type,
                                        'currency_id': liquidity_lines.currency_id.id,
                                        'destination_account_id': counterpart_lines.account_id.id if counterpart_lines else (writeoff_lines.account_id.id if len(writeoff_lines)==1 else False),
                                        'partner_id': liquidity_lines.partner_id.id,
                                })
                                if liquidity_amount > 0.0:
                                        payment_vals_to_write.update({'payment_type': 'inbound'})
                                elif liquidity_amount < 0.0:
                                        payment_vals_to_write.update({'payment_type': 'outbound'})
                        
                        move.write(move._cleanup_write_orm_values(move, move_vals_to_write))
                        pay.write(move._cleanup_write_orm_values(pay, payment_vals_to_write))

                        
        def _synchronize_to_moves(self, changed_fields):
            ''' Update the account.move regarding the modified account.payment.
            :param changed_fields: A list containing all modified fields on account.payment.
            '''
            if self._context.get('skip_account_move_synchronization'):
                return

            if not any(field_name in changed_fields for field_name in (
                'date', 'amount', 'payment_type', 'partner_type', 'payment_reference', 'is_internal_transfer',
                'currency_id', 'partner_id', 'destination_account_id', 'partner_bank_id',
                'post_type','journal_id', 'writeoff_multi_acc_ids'
            )):
                return


            for pay in self.with_context(skip_account_move_synchronization=True):
                liquidity_lines, counterpart_lines, writeoff_lines = pay._seek_for_lines()

                # Make sure to preserve the write-off amount.
                # This allows to create a new payment with custom 'line_ids'.

                if writeoff_lines:
                        counterpart_amount = sum(counterpart_lines.mapped('amount_currency'))
                        writeoff_amount = sum(writeoff_lines.mapped('amount_currency'))
                        # To be consistent with the payment_difference made in account.payment.register,
                        # 'writeoff_amount' needs to be signed regarding the 'amount' field before the write.
                        # Since the write is already done at this point, we need to base the computation on accounting values.
                        if (counterpart_amount > 0.0) == (writeoff_amount > 0.0):
                                sign = -1
                        else:
                                sign = 1
                        writeoff_amount = abs(writeoff_amount) * sign

                        write_off_line_vals = {
                                'name': writeoff_lines[0].name,
                                'amount': writeoff_amount,
                                'account_id': writeoff_lines[0].account_id.id,
                        }
                
                else:
                        write_off_line_vals = {}

                line_ids_commands = []
                # pay.write({'writeoff_multi_acc_ids':[(5)]})
                line_vals_list = pay._prepare_move_line_default_vals(write_off_line_vals=False)
                
                
                # print(stop)
                # elif 'partner_id' in changed_fields:
                #         line_ids_commands = [
                #             (1, counterpart_lines.id, line_vals_list[1]),
                #             (0, 0, line_vals_list[0])
                #         ]
                flag = True
                if len(line_vals_list) <= 2:
                        if 'journal_id' in changed_fields and len(writeoff_lines) > 1 and len(counterpart_lines)==0 and len(liquidity_lines)==0:
                                for add in line_vals_list:
                                        line_ids_commands.append((0, 0,add))
                        if counterpart_lines and liquidity_lines:
                                line_ids_commands = [
                                (1, liquidity_lines.id, line_vals_list[0]),
                                (1, counterpart_lines.id, line_vals_list[1]),
                                ]
                                flag = False
                        elif liquidity_lines:
                                line_ids_commands = [
                                (1, liquidity_lines.id, line_vals_list[0]),
                                (0, 0, line_vals_list[1])
                                ]
                        elif counterpart_lines:
                                line_ids_commands = [
                                (0, 0, line_vals_list[0]),
                                (1, counterpart_lines.id, line_vals_list[1])
                                ]
                                flag = False
                        else:
                                line_ids_commands = [
                                (0, 0, line_vals_list[0]),
                                (0, 0, line_vals_list[1])
                                ]
                                # line_ids_commands.append((0, 0, line_vals_list[1]))
                                # line_vals_list.append()
                else:
                        if liquidity_lines:
                                line_ids_commands = [
                                        (1, liquidity_lines.id, line_vals_list[0]),
                                ]
                        else:
                                line_ids_commands = [
                                        (0, 0, line_vals_list[0]),
                                ]
                for line in writeoff_lines:
                        line_ids_commands.append((2, line.id))
                if flag:
                        for line in counterpart_lines:
                                line_ids_commands.append((2, line.id))
                                # if 'partner_id' not in changed_fields:
                                #     print(s)
                                #     for line in counterpart_lines:
                                #         line_ids_commands.append((2, line.id))

                                # if writeoff_lines:
                if len(line_vals_list) < 3:
                        pass
                        # line_ids_commands.append((0, 0, line_vals_list[2]))
                else:
                        for i in range(1,len(line_vals_list)):
                                line_ids_commands.append((0, 0, line_vals_list[i]))

                # Update the existing journal items.
                # If dealing with multiple write-off lines, they are dropped and a new one is generated.
                
                pay.move_id.write({
                        'partner_id': pay.partner_id.id,
                        'currency_id': pay.currency_id.id,
                        'partner_bank_id': pay.partner_bank_id.id,
                        'line_ids': line_ids_commands,
                })

        

        def unlink(self):
                if self.state == 'posted':
                        raise UserError(_("You cannot delete an entry which has been posted once."))

                return super(AccountPayment, self).unlink()


        @api.onchange('destination_account_id')
        def get_value_require(self):
                for this in self:
                        if this.destination_account_id:

                                this.partne_required = False

                                if this.destination_account_id.is_vendor:
                                        this.partne_required = True
                                if this.destination_account_id.is_customer:
                                        this.partne_required = True
                        else:
                                this.partne_required = False

        partne_required = fields.Boolean(compute="get_value_require")

        # @api.onchange('destination_account_id','post_type','partner_id','journal_id')
        # def onchange_account_id(self):
        #         b={}
        #         for this in self:
        #             if this.destination_account_id:
        #                 if this.destination_account_id.is_customer == True:
        #                         b ={'domain': {
        #                                 'partner_id': [('is_customer', '=', True)],
        #                                 },
        #                         'required': {'partner_id': [('is_customer', '=', True)]}}
        #                 elif this.destination_account_id.is_vendor == True:
        #                         b ={'domain': {
        #                                 'partner_id': [('is_vendor', '=', True)],
        #                                 },
        #                         'required': {'partner_id': [('is_vendor', '=', True)]}}
        #                 else:
        #                         b ={'domain': {
        #                                 'partner_id': [(1,'=',1)],
        #                                  }}
        #         return b


        def write(self, vals):
            # OVERRIDE
            res = super(AccountPayment,self).write(vals)
            for rec in self:
                if rec.journal not in ['in', 'out']  and rec.is_document and not rec.is_internal_transfer:
                        raise UserError(_("Please Select Journal"))
            return res
                
class writeoff_accounts(models.Model):
    _name = 'writeoff.accounts'
    _inherit = 'analytic.mixin'

    writeoff_account_id = fields.Many2one('account.account', string="Difference Account", copy=False, required="1")
    account_type = fields.Selection(related='writeoff_account_id.account_type')

    writeoff_partner_id = fields.Many2one('res.partner', string="Partner", copy=False)
    name = fields.Char('Description')
#     analytic_account_id = fields.Many2one('account.analytic.account')
#     analytic_tag_ids = fields.Many2many('account.analytic.tag')
    amount = fields.Monetary(string='Payment Amount', required=True)
    currency_id = fields.Many2one('res.currency', string='Currency', required=True,
                                  default=lambda self: self.env.user.company_id.currency_id)
    payment_id = fields.Many2one('account.payment', string='Payment Record')

#     analytic_distribution = fields.Json(
#         'Analytic',
#         compute="_compute_analytic_distribution", store=True, copy=True, readonly=False,
#         precompute=True
#     )
#     def _compute_analytic_distribution(self):
#         pass

    # === Analytic fields === #
    analytic_line_ids = fields.One2many(
        comodel_name='account.analytic.line', inverse_name='pay_line',
        string='Analytic lines',
    )

#     plan_id = fields.Many2one('account.analytic.plan', related='payment_id.plan_id')

    analytic_account_id = fields.Many2one('account.analytic.account',
        string="Analytic Account",
        copy=False ) # Unrequired company
        # domain="[('plan_id','=',plan_id)]"
        # , '|', ('company_id', '=', False), ('company_id', '=', company_id)

    @api.onchange('analytic_account_id')
    def _onchange_analytic_account_id(self):
        for this in self:

            if this.analytic_account_id:
                this.analytic_distribution = {this.analytic_account_id.id: 100}
            else:
                this.analytic_distribution = {}


#     analytic_distribution = fields.Json(
#         inverse="_inverse_analytic_distribution",
#     ) # add the inverse function used to trigger the creation/update of the analytic lines accordingly (field originally defined in the analytic mixin)

    
#     @api.onchange('analytic_distribution')
#     def _inverse_analytic_distribution(self):
#         """ Unlink and recreate analytic_lines when modifying the distribution."""
#         lines_to_modify = self.env['account.move.line'].browse([
#             line.id for line in self if line.parent_state == "posted"
#         ])
#         lines_to_modify.analytic_line_ids.unlink()
#         lines_to_modify._create_analytic_lines()

#     def _create_analytic_lines(self):
#         """ Create analytic items upon validation of an account.move.line having an analytic distribution.
#         """
#         self._validate_analytic_distribution()
#         analytic_line_vals = []
#         for line in self:
#             analytic_line_vals.extend(line._prepare_analytic_lines())

#         self.env['account.analytic.line'].create(analytic_line_vals)

#     def _prepare_analytic_lines(self):
#         self.ensure_one()
#         analytic_line_vals = []
#         if self.analytic_distribution:
#             # distribution_on_each_plan corresponds to the proportion that is distributed to each plan to be able to
#             # give the real amount when we achieve a 100% distribution
#             distribution_on_each_plan = {}

#             for account_id, distribution in self.analytic_distribution.items():
#                 line_values = self._prepare_analytic_distribution_line(float(distribution), account_id, distribution_on_each_plan)
#                 if not float_is_zero(line_values.get("amount"), precision_digits=self.env.company.currency_id.decimal_places):
#                     analytic_line_vals.append(line_values)
#         return analytic_line_vals

#     def _prepare_analytic_distribution_line(self, distribution, account_id, distribution_on_each_plan):
#         """ Prepare the values used to create() an account.analytic.line upon validation of an account.move.line having
#             analytic tags with analytic distribution.
#         """
#         self.ensure_one()
#         account_id = int(account_id)
#         account = self.env['account.analytic.account'].browse(account_id)
#         distribution_plan = distribution_on_each_plan.get(account.root_plan_id, 0) + distribution
#         if self.env.company.currency_id.compare_amounts(distribution_plan, 100) == 0:
#             amount = -self.balance * (100 - distribution_on_each_plan.get(account.root_plan_id, 0)) / 100.0
#         else:
#             amount = -self.balance * distribution / 100.0
#         distribution_on_each_plan[account.root_plan_id] = distribution_plan
#         default_name = self.name or (self.ref or '/' + ' -- ' + (self.partner_id and self.partner_id.name or '/'))
#         return {
#             'name': default_name,
#             'date': self.date,
#             'account_id': account_id,
#             'partner_id': self.partner_id.id,
#             'unit_amount': self.quantity,
#             'product_id': self.product_id and self.product_id.id or False,
#             'product_uom_id': self.product_uom_id and self.product_uom_id.id or False,
#             'amount': amount,
#             'general_account_id': self.account_id.id,
#             'ref': self.ref,
#             'move_line_id': self.id,
#             'user_id': self.move_id.invoice_user_id.id or self._uid,
#             'company_id': account.company_id.id or self.company_id.id or self.env.company.id,
#         }


#     required_analytical = fields.Boolean(related='writeoff_account_id.required_analytical')    
    

    flag = fields.Boolean(default=False, compute='_compute_partner_filter_multi_lines')

    @api.onchange('writeoff_account_id')
    def _compute_partner_filter_multi_lines(self):
        b={}
        for rec in self:
            user_flag = self.env['res.users'].has_group('account.group_account_invoice')
            user_flag1 = self.env['res.users'].has_group('__export__.res_groups_132_f7b30e69')
            if user_flag1 == True:
                rec.flag = True
                b ={'domain': {
                        'writeoff_partner_id': ['|',('company_id','=',False),('company_id','=',self.env.company.id)],
                    }}
            elif user_flag == True:
                rec.flag = True
                b ={'domain': {
                        'writeoff_partner_id': ['|',('company_id','=',False),('company_id','=',self.env.company.id)],
                    }}
            else:
                rec.flag = False
                b ={'domain': {
                        'writeoff_partner_id': ['|',('company_id','=',False),('company_id','=',self.env.company.id),('user_id', '=', self.env.user.id)],
                    }}
        return b

        
    @api.onchange('writeoff_account_id')
    def get_value_require(self):
        for this in self:
            if this.writeoff_account_id:

                this.partne_required = False
                # this.analytic_req = False
                # if this.writeoff_account_id.code[0] == '5':
                #         this.analytic_req = True
                # else:
                #         this.analytic_req = False

                if this.writeoff_account_id.is_vendor:
                    this.partne_required = True
                if this.writeoff_account_id.is_customer:
                    this.partne_required = True
            else:
                this.partne_required = False
                # this.analytic_req = False
    partne_required = fields.Boolean(compute="get_value_require")

#     @api.onchange('writeoff_account_id')
#     def onchange_account_id(self):
#         b={}
#         for this in self:
#             if this.writeoff_account_id.is_customer == True:
#                 b ={'domain': {'writeoff_partner_id': [('is_customer', '=', True)]},
#                     'required': {'writeoff_partner_id': [('is_customer', '=', True)]}}
#             elif this.writeoff_account_id.is_vendor == True:
#                 b ={'domain': {'writeoff_partner_id': [('is_vendor', '=', True)]},
#                     'required': {'writeoff_partner_id': [('is_vendor', '=', True)]}}
                    
#             else:
#                 b ={'domain': {'writeoff_partner_id': [(1,'=',1)]}}
#             return b



class AccountJournal(models.Model):
    _inherit = 'account.journal'

    pay_type = fields.Selection([('in','IN'),('out','OUT')])


class AccountAccount(models.Model):

    _inherit = 'account.account'

    is_customer = fields.Boolean('Customer')
    is_vendor = fields.Boolean('Vendor')


class AccountAnalyticLine(models.Model):
    _inherit = "account.analytic.line"

    
    pay_line = fields.Many2one('writeoff.accounts', string='Pay Item')
 
