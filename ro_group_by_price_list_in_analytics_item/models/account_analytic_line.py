from odoo import fields, models,api


class AccountAnalyticLine(models.Model):
    _inherit = 'account.analytic.line'
    
    ro_pricelist=fields.Many2one('product.pricelist',string='Pricelist',compute='_compute_pricelist',store=True)
    
    @api.depends('move_line_id.sale_line_ids.order_id.pricelist_id')
    def _compute_pricelist(self):
        for record in self:
            record.ro_pricelist = record.move_line_id.sale_line_ids.order_id.pricelist_id
            
        

