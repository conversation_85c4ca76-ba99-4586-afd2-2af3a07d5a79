# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.osv import expression
from odoo.exceptions import UserError
from odoo.addons import account



# class AccountPaymentMethod(models.Model):
#     _name = "account.payment.method"
AccountPaymentMethod = account.models.account_payment_method.AccountPaymentMethod

@api.model
def _get_payment_method_domain(self, code):
    """
    :return: The domain specyfying which journal can accomodate this payment method.
    """
    if not code:
        return []
    information = self._get_payment_method_information().get(code)
    currency_ids = False
    country_id = False
    if information:
        currency_ids = information.get('currency_ids')
        country_id = information.get('country_id')
        default_domain = [('type', 'in', ('bank', 'cash'))]
        domains = [information.get('domain', default_domain)]
    else:
        default_domain = [('type', 'in', ('bank', 'cash'))]
        domains = [default_domain]

    if currency_ids:
        domains += [expression.OR([
            [('currency_id', '=', False), ('company_id.currency_id', 'in', currency_ids)],
            [('currency_id', 'in', currency_ids)]],
        )]

    if country_id:
        domains += [[('company_id.account_fiscal_country_id', '=', country_id)]]

    return expression.AND(domains)

AccountPaymentMethod._get_payment_method_domain = _get_payment_method_domain