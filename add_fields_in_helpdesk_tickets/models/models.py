# -*- coding: utf-8 -*-

from odoo import models, fields, api


class HelpdeskTicket(models.Model):
    _inherit = 'helpdesk.ticket'

    confirmation_call_status = fields.Selection(
        string='Confirmation Call Status',
        selection=[('answered', 'Answered'), ('unreachable_1', 'Unreachable 1'), ('unreachable_2', 'Unreachable 2') , ('unreachable_3', 'Unreachable 3')
                   , ('call_back', 'Call Back'), ('send_whatsapp_1', 'Sent WhatsApp 1') , ('send_whatsapp_2', 'Sent WhatsApp 2') , ('send_whatsapp_3', 'Sent WhatsApp 3')]
    )
    
    confirmation_call_feedback = fields.Selection(
        string='Confirmation Call Feedback',
        selection=[('confirmed', 'Confirmed'), ('cancelled', 'Cancelled')
                   , ('reschedule', 'Reschedule') , ('not_confirmed', 'Not confirmed')]
    )

    branch = fields.Selection(
        string='Branch',
        selection=[('zayed', 'Zayed'), ('roxy', 'Roxy')
                   , ('nasr_city', 'Nasr City') , ('mohandseen ', 'Mohan<PERSON>een'), ('maadi','<PERSON><PERSON>'), ('cmc','CMC')]
    )

    reason_for_cancellation = fields.Selection(
        string='Reason For Cancellation',
        selection=[('change_his_her_mind', 'Change his /her mind'), ('personal_issue', 'Personal isuue') 
                   , ('high_price ', 'High Price'), ('other','Other')]
    )

    # **********************************No show form*************************************************

    no_show_call_status = fields.Selection(
        string='No Show Call Status',
        selection=[('answered', 'Answered'), ('unreachable_1', 'Unreachable 1'), ('unreachable_2', 'Unreachable 2') 
                    , ('call_back', 'Call Back'), ('missed_no_call', 'Missed - No call') , ('sent_whatsapp', 'Sent WhatsApp')
                    , ('already_has_another_appointment', 'Already has another appointment ')]
    )

    reason_for_no_show = fields.Selection(
        string='Reason For No-show',
        selection=[("type1", "Didn't receive the confirmation Call"), ("type2", "Personal Issue"),
                    ("type3", "Forgot the appointment"), ("type4", "Didn't book the appointment"),
                    ("type5", "Already arrived"), ("type6", "Other"),
                    ("type7", "Insurance Rejected his\her approval"), ("type8", "Waiting for approval insurance"),
                    ("type9", "Branch issue")]
    )

    new_booking_date = fields.Date(string="New Booking Date")

    no_show_call_feedback = fields.Selection(
        string='No Show Call Feedback',
        selection=[('booked ', 'Booked '), ('not_interested', 'Not interested ')
                   , ('already_arrived', 'Already arrived ') , ('need_follow_up', 'Need follow up ')]
    )

    reason_for_not_interested = fields.Selection(
        string='Reason for not interested',
        selection=[('high_price ', 'High Price'), ('started_with_competitors', 'started with competitors')
                   , ('bad_experience', 'Bad experience') , ('didnot_book_appointment', "Didn't book the appointment")
                   , ('other ', 'Other')]
    )

    #**********************************************Inbound form******************************************************************

    caller_mobile_number = fields.Char(string='Caller Mobile Number')

    patient_type = fields.Selection(string="Patient Type", 
        selection=[('new_patient', 'New patient'), ('existing_patient', 'Existing patient')] )
    
    # is_there_any_contract = fields.Selection(
    #     string="Is there any contract with the company Shiny White", 
    #     selection=[('direct', 'Direct'), ('indirect', 'Indirect')] )

    main_call_type = fields.Selection(
        string='Main Call Type',
        selection=[('inquiry', 'Inquiry'), ('request', 'Request')
                   , ('complaint', 'Complaint') , ('irrelevant_call', 'Irrelevant call')]
    )

    escalation_needed = fields.Selection(string="Escalation Needed", 
        selection=[('yes', 'Yes'), ('no', 'No')] )
    
    how_you_know_us = fields.Selection(string="How you know us", 
        selection=[('tiktok', 'Tiktok'), ('snapchat', 'Snapchat'),('instagram', 'Instagram'), ('twitter', 'Twitter'),
                   ('fb', 'FB'), ('youtube', 'YouTube'),('whatsapp', 'WhatsAPP'), ('viber', 'Viber'),
                   ('linkedin', 'Linkedin'), ('website', 'Website'),('ads_on_tv', 'Ads on TV'), ('billboards', 'Billboards'),
                   ('radio', 'Radio'), ('word_of_mouth', 'Word of Mouth'),('friends_family', 'Friends & Family'),
                   ('smscampaign', 'SMS Campaign'),
                   ('beauty_website', 'موقع تجميلى'), ('insurance', 'Insurance'),('other', 'Other')] )
    

    inquiry_type = fields.Selection(string="Inquiry type", 
        selection=[('type1', 'Inquiry about the diagnosis price'), ('type2', 'Inquiry about the installment plan'),
                    ('type3', 'Inquiry about Shiny white addresses/working hours'), ('type4', 'Inquiry about Associations and treatment project'), 
                    ('type5', 'Inquiry about diagnosis online'), ('type6', 'Inquiry about the insurance approval status'), 
                    ('type7', 'Inquiry about Next appointment details'), ('type8', 'Inquiry about the Rest of payments'), 
                    ('type9', 'Inquiry about the Service price'), ('type10', 'Inquiry about specific doctor schedule'), 
                    ('type11', 'Inquiry about if the invoice available'), ('type12', 'Inquiry about if the prosthesis available'), 
                    ('type13', 'Other')] )
    
    inquiry_service_type = fields.Selection(string="Inquiry Service Type", 
        selection=[('type1', 'Implant'), ('type2', 'Fixed Prosthesis'),
                   ('type3', 'Removable prosthesis'), ('type4', 'Snap on smile'),
                   ('type5', 'Hollywood smile'), ('type6', 'Bleaching'),
                   ('type7', 'Composite'), ('type8', 'ENDO'),
                   ('type9', 'Ortho'), ('type10', 'Scaling'),
                   ('type11', 'Extraction'), ('type12', 'Laser Esthetic'),
                   ('type13', 'Full Arch-Implants'), ('type14', 'Full Arch-Veneers'),
                   ('type15', 'GA'), ('type16', 'Other'),] )
    
    request_type = fields.Selection(string="Request type", 
        selection=[('type1', 'Wants to book diagnosis/Appointment'), ('type2', 'Reschedule the booking appointment'),
                   ('type3', 'Wants to cancel the the booking appointment'), ('type4', 'Wants to confirm her/ his appointment'),
                   ('type5', 'Wants to contact with "commercial- finance -Administration- Marketing "'), ('type6', 'Wants to contact with doctor or branch'),
                   ('type7', 'Wants to transfer to another branch'), ('type8', 'Wants to Cancel the approval insurance'),
                   ('type9', 'Wants the TTT \CBCT'), ('type10', 'Wants the medical report'),
                   ('type11', 'Wants to refund amount'), ('type12', 'CST Wants the invoice'),
                   ('type13', 'Other')] )
    
    complaint_category = fields.Selection(string="Complaint Category", 
        selection=[('behavior', 'Behavior'), ('finance', 'Finance'),
                   ('process', 'Process'), ('quality', 'Quality')] )
    
    complaint_related_to_behavior = fields.Selection(string="Complaint Related to behavior", 
        selection=[('type1', "Complaint from receptionist's bad behavior"), 
                   ('type2', "Complaint from branch manager's bad behavior"),
                   ('type3', "Complaint from doctor's bad behavior"), ('type4', 'Complaint from clinic manager'), 
                   ('type5', 'Other')] )
    
    complaint_related_to_finance = fields.Selection(string="Complaint Related to Finance", 
        selection=[('type1', "CST Paid money without reason"), 
                   ('type2', "Didn’t receive the receipt"),
                   ('type3', "CST didn't receive the remaining money"), 
                   ('type4', 'Other')] )
    
    complaint_related_to_process = fields.Selection(string="Complaint Related to Process", 
        selection=[('type1', "Delay receiving the prosthesis"), 
                   ('type2', "Didn’t receive confirmation call"),
                   ('type3', "CST complain from waiting time"), 
                   ('type4', 'Other')] )
    

    complaint_related_to_quality = fields.Selection(string="Complaint Related to Quality", 
        selection=[('type1', "Quality Service"), 
                   ('type2', 'Other')] )
    
    quality_service_type = fields.Selection(string="Quality Service Type", 
        selection=[('type1', 'Implant'), ('type2', 'Fixed Prosthesis'),
                   ('type3', 'Removable prosthesis'), ('type4', 'Snap on smile'),
                   ('type5', 'Hollywood smile'), ('type6', 'Bleaching'),
                   ('type7', 'Composite'), ('type8', 'ENDO'),
                   ('type9', 'Ortho'), ('type10', 'Scaling'),
                   ('type11', 'Extraction'), ('type12', 'Laser Esthetic'),
                   ('type13', 'Full Arch-Implants'), ('type14', 'Full Arch-Veneers'),
                   ('type15', 'GA'), ('type16', 'Other'),] )
    
    