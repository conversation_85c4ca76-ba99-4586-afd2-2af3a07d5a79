# -*- coding: utf-8 -*-

from odoo import api, fields, models, _

READONLY_FIELD_STATES = {
    state: [('readonly', True)]
    for state in {'sale', 'done', 'cancel'}
}

class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    def get_domain_usd(self):
        return [('currency_id', '=', self.env.ref('base.USD').id), '|', ('company_id', '=', False), ('company_id', '=', self.env.company)]
    usd_pricelist_id = fields.Many2one('product.pricelist', domain=get_domain_usd,states=READONLY_FIELD_STATES,
        tracking=1,store=True, readonly=False)

    @api.onchange('usd_pricelist_id')
    def _onchange_usd_pricelist(self):
        self.order_line._update_usd_amount()



    def recompute_total_usd_amount(self):
        for rec in self:
            if rec.usd_pricelist_id:
                downpayments = rec.order_line.filtered(lambda x: x.is_downpayment)
                not_downpayments = rec.order_line.filtered(lambda x: not x.is_downpayment)

                total_down_payment_usd = sum(downpayments.mapped('usd_price_unit'))
                total_down_payment_eg = sum(downpayments.mapped('price_unit'))

                total_amt_usd = sum(not_downpayments.mapped(lambda x: x.usd_price_unit * x.product_uom_qty))

                total_amt_eg = sum(not_downpayments.mapped('price_subtotal'))


                remaining_usd = total_amt_usd - total_down_payment_usd

                total_down_payment_eg

                usd_id = self.env.ref('base.USD')
                date = fields.Date.today()
                price_amt = usd_id._convert(remaining_usd, rec.currency_id, rec.company_id, date)

                new_total_eg = price_amt + total_down_payment_eg

                to_split = new_total_eg - total_amt_eg

                factor = (to_split / total_amt_eg) if total_amt_eg else 0

                for line in not_downpayments:
                        line.price_unit = line.price_unit + (factor * line.price_unit)


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    usd_price_unit = fields.Float()

    @api.onchange('usd_price_unit')
    def _onchange_usd_amount(self):
        for rec in self:
            if not rec.is_downpayment:
                # TODO: check line and get usd rate
                usd_id = self.env.ref('base.USD')

                date = fields.Date.today()
                price_amt = usd_id._convert(rec.usd_price_unit, rec.currency_id, rec.company_id, date)

                rec.price_unit = price_amt

    def _prepare_invoice_line(self, **optional_values):
        if self.order_id.usd_pricelist_id:
            self.order_id.recompute_total_usd_amount()
        res = super(SaleOrderLine, self)._prepare_invoice_line(**optional_values)
        res['usd_price_unit'] = self.usd_price_unit
        return res

    @api.depends('product_id', 'product_uom', 'product_uom_qty')
    def _compute_price_unit(self):
        super(SaleOrderLine, self)._compute_price_unit()
        to_update_usd = self.filtered(lambda x:x.order_id.usd_pricelist_id)
            
        to_update_usd._update_usd_amount()
 
    def _update_usd_amount(self):
        for rec in self:
            price = 0
            if rec.order_id.usd_pricelist_id and not rec.is_downpayment and not rec.display_type:
                price, rule_id = rec.order_id.usd_pricelist_id._get_product_price_rule(rec.product_id, rec.product_uom_qty, rec.product_uom, rec.order_id.date_order)
            rec.usd_price_unit = price
            rec._onchange_usd_amount()
            

class SaleAdvancePaymentInv(models.TransientModel):
    _inherit = 'sale.advance.payment.inv'

    usd_price_unit = fields.Float()

    @api.onchange('usd_price_unit')
    def _onchange_usd_amount(self):
        for rec in self:
            if rec.advance_payment_method == 'fixed':
                # TODO: check line and get usd rate
                usd_id = self.env.ref('base.USD')

                date = fields.Date.today()
                price_amt = usd_id._convert(rec.usd_price_unit, rec.currency_id, rec.company_id, date)

                rec.fixed_amount = price_amt

                # if rec.sale_order_ids:
                #     rec.sale_order_ids.order_line._onchange_usd_amount()
   

    def _prepare_so_line_values(self, order):
        res = super(SaleAdvancePaymentInv, self)._prepare_so_line_values(order)
        res['usd_price_unit'] = self.usd_price_unit
        return res
