<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ro_view_product_price_edit_groups" model="ir.ui.view">
		<field name="name">product.template.form</field>
		<field name="model">product.template</field>
		<field name="inherit_id" ref="product.product_template_only_form_view"/>
		<field name="arch" type="xml">
			<xpath expr="//field[@name='list_price']" position="before">
				<field name="ro_can_edit_price" invisible="1"/>
			</xpath>
			<xpath expr="//field[@name='list_price']" position="attributes">
				<attribute name="attrs">{'readonly': [('ro_can_edit_price', '!=', True)]}</attribute>
				<attribute name="force_save">1</attribute>
			</xpath>
		</field>
	</record>

	<record id="ro_view_product_price_tree" model="ir.ui.view">
		<field name="name">product.template.tree</field>
		<field name="model">product.template</field>
		<field name="inherit_id" ref="product.product_template_tree_view"/>
		<field name="arch" type="xml">
			<xpath expr="//field[@name='list_price']" position="attributes">
				<attribute name="readonly">1</attribute>
			</xpath>
		</field>
	</record>

</odoo>
