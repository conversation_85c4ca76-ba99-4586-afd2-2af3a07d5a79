<?xml version="1.0" encoding="utf-8"?>
<odoo>
	<record id="ro_view_product_variant_extra_price_edit_groups" model="ir.ui.view">
		<field name="name">view.product.variant.extra.price.edit.groups</field>
		<field name="model">product.pricelist.item</field>
		<field name="inherit_id" ref="product.product_pricelist_item_tree_view_from_product" />
		<field name="arch" type="xml">
			<xpath expr="//field[@name='fixed_price']" position="before">
				<field name="ro_can_edit_price" invisible="1"/>
			</xpath>
			<xpath expr="//field[@name='fixed_price']" position="attributes">
				<attribute name="attrs">{'readonly': [('ro_can_edit_price', '!=', True)]}</attribute>
				<attribute name="force_save">1</attribute>
			</xpath>
		</field>
	</record>
</odoo>