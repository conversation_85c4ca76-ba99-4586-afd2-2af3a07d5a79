# -*- coding: utf-8 -*-

from odoo import fields, models, _
from odoo.exceptions import ValidationError


class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    ro_can_edit_price = fields.Boolean(compute='get_can_edit_price')

    def get_can_edit_price(self):
        if self.env.user.has_group('ro_control_edit_of_product_sales_price.ro_edit_product_sale_price'):
            self.ro_can_edit_price = True    

        else:
            self.ro_can_edit_price = False
            
    def write(self, vals):
        
        if vals.get('list_price') and not self.env.user.has_group('ro_control_edit_of_product_sales_price.ro_edit_product_sale_price') \
            and self.list_price:

            raise ValidationError(_("you can't edit sale price please contact your admin"))

        return super(ProductTemplate, self).write(vals)


class ProductProduct(models.Model):
    _inherit = 'product.product'
    
    
    def write(self, vals):
        if vals.get('list_price') and not self.env.user.has_group('ro_control_edit_of_product_sales_price.ro_edit_product_sale_price') \
            and self.list_price:

            raise ValidationError(_("you can't edit sale price please contact your admin"))

        return super(ProductProduct, self).write(vals)

class ProductPricelistItem(models.Model):
    _inherit = 'product.pricelist.item'
    

    ro_can_edit_price = fields.Boolean(compute='get_can_edit_price')

    def get_can_edit_price(self):
        if self.env.user.has_group('ro_control_edit_of_product_sales_price.ro_edit_product_sale_price'):
            self.ro_can_edit_price = True    

        else:
            self.ro_can_edit_price = False
