<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="stock_location_route_form_view_inherit_add_so_warehouse" model="ir.ui.view">
            <field name="name">stock.route.view.form.inherit</field>
            <field name="model">stock.route</field>
            <field name="inherit_id" ref="stock.stock_location_route_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='supplied_wh_id']" position="after">
                    <field name="sale_warehouse" />
                </xpath>
            </field>
        </record>

        <record id="view_order_form_inherit_add_domain_in_rents" model="ir.ui.view">
            <field name="name">sale.order.view.form.inherit</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='order_line']/tree/field[@name='route_id']" position="after">
                    <field name="warehouse_id" invisible="1"/>
                </xpath>
                <xpath expr="//field[@name='order_line']/tree/field[@name='route_id']" position="attributes">
                    <attribute name="domain">[('sale_warehouse','=',warehouse_id)]</attribute>
                </xpath>
            </field>
        </record>
    </data>
    
</odoo>
