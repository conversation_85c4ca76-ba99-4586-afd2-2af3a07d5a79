# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class HrContract(models.Model):
    _inherit = 'hr.contract'
    
    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id)
    ro_basic_salary = fields.Monetary(string='Basic salary',currency_field='currency_id')
    
    ro_insurance_salary = fields.Monetary(string='insurance salary',currency_field='currency_id')
    
    ro_other_allowance = fields.Monetary(string='other allowance',currency_field='currency_id')
    
    ro_Transportation_allowance = fields.Monetary(string='Transportation allowance',currency_field='currency_id')
    
    ro_kpi = fields.Monetary(string='KPI',currency_field='currency_id')
    
    ro_Commitment = fields.Monetary(string='Commitment ',currency_field='currency_id')
    
    


   
    
    