# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import _, fields, models

class CrmValidation(models.TransientModel):
    _name = 'crm.validation'
    _description = 'Crm Validation'

    crm_lead_id = fields.Many2one('crm.lead')

    def confirm_stage(self):
        stages = self.env['crm.stage'].search([]).ids
        next_stage = stages.index(self.crm_lead_id.stage_id.id)
        self.crm_lead_id.stage_id = stages[next_stage+1]

