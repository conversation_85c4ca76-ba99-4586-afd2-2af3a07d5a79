<odoo>
  <data>
    <record id="crm_lead_view_stages_form_inherit" model="ir.ui.view">
      <field name="name">CRM Lead stages</field>
      <field name="model">crm.lead</field>
      <field name="inherit_id" ref="crm.crm_lead_view_form"/>
      <field name="priority">18</field>
      <field name="arch" type="xml">

        <xpath expr="//button[@name='action_sale_quotations_new']" position="before">
          <button string="Next Stage" name="action_next_stage" type="object" class="oe_highlight" data-hotkey="n" title="Go to next stage" attrs="{'invisible': ['|',('is_won', '=', True), '&amp;',  ('stage_approval', '=', True), ('is_approved', '=', False)]}" />
          <button string="Approve" name="action_approve" type="object" class="oe_highlight" title="Approve" attrs="{'invisible': ['|' , ('stage_approval', '=', False), ('is_approved', '=', True)]}" />
        </xpath>
      
        <xpath expr="//button[@name='action_set_won_rainbowman']" position="replace"/>

        <xpath expr="//div[hasclass('oe_title')]/h1" position="before">
          <h2><field class="o_text_overflow" name="sequence" readonly='1' force_save='1'/></h2>

          <field name="show_quotation" invisible='1' />
          <field name="ro_readonly_stage" invisible='1' />
          
          <field name="is_won" invisible='1' />
          <field name="stage_approval" invisible='1' />
          <field name="is_approved" invisible='1' />
        </xpath>

        <xpath expr="//field[@name='name']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='probability']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//group[@name='opportunity_partner']//field[@name='payment_term_id']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//group[@name='opportunity_partner']//field[@name='pricelist_id']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//group[@name='opportunity_partner']//field[@name='usd_pricelist_id']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//group[@name='lead_partner']//field[@name='pricelist_id']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//group[@name='lead_partner']//field[@name='usd_pricelist_id']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//group[@name='opportunity_partner']//field[@name='email_from']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//group[@name='opportunity_partner']//field[@name='phone']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        
        <xpath expr="//field[@name='contruct_date']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        
        <xpath expr="//group[@name='opportunity_partner']//field[@name='partner_id']" position="attributes">
          <attribute name="string">Patient</attribute>
          <!-- <attribute name="default_focus">1</attribute> -->
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>

        <xpath expr="//page[@name='order_lines']/field[@name='crm_line']/tree/field[@name='sequence']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//page[@name='order_lines']/field[@name='crm_line']/tree/field[@name='product_id']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//page[@name='order_lines']/field[@name='crm_line']/tree/field[@name='name']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//page[@name='order_lines']/field[@name='crm_line']/tree/field[@name='product_uom_qty']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//page[@name='order_lines']/field[@name='crm_line']/tree/field[@name='price_unit']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//page[@name='order_lines']/field[@name='crm_line']/tree/field[@name='usd_price_unit']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//page[@name='order_lines']/field[@name='crm_line']/tree/field[@name='tax_id']" position="attributes">
          <attribute name="attrs">{'readonly': [['ro_readonly_stage', '=', True]]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>

        <xpath expr="//page[@name='order_lines']/field[@name='crm_line']/tree/field[@name='sequence']" position="before">
          <field name="ro_readonly_stage" invisible='1' />
        </xpath>

        <xpath expr="//button[@name='action_sale_quotations_new']" position="attributes">
          <attribute name="attrs" >{'invisible': ['|' ,'|' , ('show_quotation', '=', False), ('type', '=', 'lead'), '&amp;', ('probability', '=', 0), ('active', '=', False)]}</attribute>
        </xpath>

        <xpath expr="//field[@name='stage_id']" position="attributes">
          <attribute name="options" >{'clickable': False, 'fold_field': 'fold'}</attribute>
        </xpath>
      </field>
    </record>

    <!-- Hide expected_revenue -->
    <record id="crm_lead_hide_expected_revenue_inherit" model="ir.ui.view">
      <field name="model">crm.lead</field>
      <field name="inherit_id" ref="crm.crm_lead_view_form"/>
      <field name="arch" type="xml">
        <xpath expr="//h2/div[1]" position="attributes">
            <attribute name="invisible">True</attribute>
        </xpath>
      </field>
    </record> 


    <record id="crm_lead_view_stages_kanban_inherit" model="ir.ui.view">
      <field name="name">CRM Lead kanban stages</field>
      <field name="model">crm.lead</field>
      <field name="inherit_id" ref="crm.crm_case_kanban_view_leads"/>
      <field name="arch" type="xml">
        <xpath expr="//kanban" position="attributes">
          <attribute name="on_create" remove="quick_create" ></attribute>
          <attribute name="records_draggable">False</attribute>
          <attribute name="quick_create">0</attribute>          
        </xpath>
      </field>
    </record>

  </data>
</odoo>