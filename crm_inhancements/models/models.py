# -*- coding: utf-8 -*-

from lxml import etree
import simplejson

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.osv import expression


class CrmLeadLine(models.Model):
    _inherit = 'crm.lead.line'

    ro_readonly_stage = fields.Boolean(related='crm_id.ro_readonly_stage')
class CrmLead(models.Model):
    _inherit = 'crm.lead'

    sequence = fields.Char(copy=False)
    # default=lambda item: item.get_default_sequence(),


    show_quotation = fields.<PERSON>olean(related='stage_id.show_quotation')
    is_won = fields.<PERSON><PERSON>an(related='stage_id.is_won')

    ro_readonly_stage = fields.Bo<PERSON>an(related='stage_id.ro_readonly_stage')

    stage_approval = fields.Boolean(related='stage_id.stage_approval')
    users_to_approve = fields.Many2many(related='stage_id.users_to_approve')

    print_contract = fields.<PERSON><PERSON>an(related='stage_id.print_contract')
    is_approved = fields.<PERSON><PERSON><PERSON>()

    def action_approve(self):
        for this in self:
            if self.env.user.id in this.users_to_approve.ids:
                this.is_approved = True
            else:
                raise ValidationError("You not allowed to approve")

    def action_next_stage(self):
        for this in self:

            if this.stage_id.show_quotation:
                view = self.env.ref('crm_inhancements.view_crm_validation')

                return {
                    'name': _('Validate Stage?'),
                    'type': 'ir.actions.act_window',
                    'view_mode': 'form',
                    'res_model': 'crm.validation',
                    'views': [(view.id, 'form')],
                    'view_id': view.id,
                    'target': 'new',
                    'context': dict(self.env.context, default_crm_lead_id=this.id),
                }
            else:
                stages = self.env['crm.stage'].search([]).ids
                next_stage = stages.index(this.stage_id.id)
                this.stage_id = stages[next_stage+1]

    # @api.model
    # def _get_view(self, view_id=None, view_type=False, **options):
    #     arch, view = super()._get_view(
    #         view_id=view_id, view_type=view_type, **options)
    #     doc = arch
    #     if view_type == 'form':
    #         for node in doc.xpath("//field"):
    #             modifiers = node.get("attrs")
    #             if modifiers:
    #                 if 'readonly' not in modifiers:
    
    #                     modifiers = "{'readonly': [['ro_readonly_stage', '=', True]]}"
    #                     node.set('force_save', '1')
    
    #                 elif type(modifiers['readonly']) != bool:
    
    #                     modifiers['readonly'] += ['|',
    #                                               ['ro_readonly_stage', '=', True]]
    #                     node.set('force_save', '1')

    #                 node.set('attrs', modifiers)
    #             else:
    #                 modifiers = {'readonly': [['ro_rseadonly_stage', '=', True]]}
    
    #                 node.set('attrs', "{'readonly': [['ro_readonly_stage', '=', True]]}")
    #                 node.set('force_save', '1')            
    #     return doc, view


    @api.model
    def create(self, vals):
        if 'company_id' in vals:
            self = self.with_company(vals['company_id'])

        vals['sequence'] = self.env['ir.sequence'].next_by_code(
                'crm_contracting_seq')
        
        result = super(CrmLead, self).create(vals)
        return result

    # def write(self, values):
    #     for this in self:
    #         if this.stage_id and this.stage_id.stage_approval and not ( self.env.user.id in this.stage_id.users_to_approve.ids) and 'stage_id' in values:
    #             raise ValidationError("You not Allowed to move to this stage")
    #             return False
    #     return super(CrmLead, self).write(values)

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        elif operator in ('ilike', 'like', '=', '=like', '=ilike'):
            domain = expression.AND([
                args or [],
                ['|', ('name', operator, name), ('sequence', operator, name)]
            ])
            return self._search(domain, limit=limit, access_rights_uid=name_get_uid)
        return super(CrmLead, self)._name_search(name, args=args, operator=operator, limit=limit, name_get_uid=name_get_uid)



class CrmStage(models.Model):
    _inherit = 'crm.stage'

    show_quotation = fields.Boolean()
    stage_approval = fields.Boolean()
    print_contract = fields.Boolean('Print Contract')
    ro_readonly_stage = fields.Boolean('Read Only Stage')
    ro_print_crm_report = fields.Boolean('Print Crm Report')

    users_to_approve = fields.Many2many('res.users')
