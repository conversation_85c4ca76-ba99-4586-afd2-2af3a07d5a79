# -*- coding: utf-8 -*-
{
    'name': "Crm Lead Products, Place",
    'summary': """
        Crm Lead Products, Place and pass them to sale order,
        Make qu readonly coming from opportunity except delivery date and qty,
        """,
    'author': "Roaya",
    'website': "https://www.roayadm.com",
    'license': 'OPL-1',
    'category': 'CRM',
    'version': '15.0',
    'depends': ['sale_crm', 'sale', 'hide_sale_price','sale_order_customer_balance','ro_so_invoice_rates','sale_discount_limit','handel_form_with_help_desk','add_tags_in_so_line'],
    # always loaded
    'data': [
        'security/ir.model.access.csv',
        'views/views.xml',
    ],
    "external_dependencies": {"python": ['simplejson']},
}
