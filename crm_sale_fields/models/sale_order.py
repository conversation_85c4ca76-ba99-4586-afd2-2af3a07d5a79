# -*- coding: utf-8 -*-

from lxml import etree
import simplejson

from odoo import models, fields, api, _
from odoo.addons.sale.models.sale_order import SaleOrder
from odoo.tools import html_keep_url, is_html_empty


@api.depends('partner_id')
def _compute_pricelist_id(self):
    for order in self:
        if not order.partner_id:
            order.pricelist_id = False
            continue
        order = order.with_company(order.company_id)
        # order.pricelist_id = order.partner_id.property_product_pricelist

        if not order.opportunity_id:
            order.pricelist_id = order.partner_id.property_product_pricelist
        else:
            order.pricelist_id = order.opportunity_id.pricelist_id

SaleOrder._compute_pricelist_id = _compute_pricelist_id


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    opportunity_id = fields.Many2one(readonly=True)
    customer_ref = fields.Char(related='partner_id.ref')

    # distance_amt = fields.Float()
    # place = fields.Char()

    crm_sale_flag = fields.Boolean()

    pricelist_id = fields.Many2one(
        'product.pricelist', string='Pricelist', check_company=True,  # Unrequired company
        required=True,
        # required=True, readonly=True,
        states={'draft': [('readonly', False)], 'sent': [('readonly', False)]},
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]", tracking=1,
        help="If you change the pricelist, only newly added lines will be affected.")


# class SaleOrderLine(models.Model):
#     _inherit = 'sale.order.line'

#     @api.onchange('product_uom', 'product_uom_qty')
#     def product_uom_change(self):
#         if not self.order_id.crm_sale_flag:
#             super(SaleOrderLine, self).product_uom_change()
