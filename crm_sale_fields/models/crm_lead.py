# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, Command
from odoo.tools.misc import get_lang
import json
from odoo.exceptions import UserError


class crmLine(models.Model):
    _name = 'crm.lead.line'
    _describtion = "Crm Lead Line"
    _order = 'crm_id, sequence, id'

    company_id = fields.Many2one(
        related='crm_id.company_id', string='Company', store=True, index=True)

    crm_id = fields.Many2one('crm.lead')
    name = fields.Text(string='Description', required=True)
    sequence = fields.Integer(string='Sequence', default=10)

    product_id = fields.Many2one('product.product')
    price_unit = fields.Float(
        string="Unit Price",
        compute='_compute_price_unit',
        digits='Product Price',
        store=True, readonly=False, required=True, precompute=True)

    product_uom_qty = fields.Float(
        string='Quantity', digits='Product Unit of Measure', required=True, default=1.0)
    product_uom_category_id = fields.Many2one(
        related='product_id.uom_id.category_id')
    product_uom = fields.Many2one('uom.uom', string='Unit of Measure',
                                  domain="[('category_id', '=', product_uom_category_id)]", ondelete="restrict")

    currency_id = fields.Many2one(related='crm_id.company_currency', depends=[
                                  'crm_id.company_currency'], store=True, string='Currency')

    # Tech field caching pricelist rule used for price & discount computation
    pricelist_item_id = fields.Many2one(
        comodel_name='product.pricelist.item',
        compute='_compute_pricelist_item_id')

    price_subtotal = fields.Monetary(
        compute='_compute_amount', string='Subtotal', store=True)
    price_tax = fields.Float(compute='_compute_amount',
                             string='Total Tax', store=True)
    price_total = fields.Monetary(
        compute='_compute_amount', string='Total', store=True)

    tax_id = fields.Many2many('account.tax', string='Taxes', domain=[
                              '|', ('active', '=', False), ('active', '=', True)])
    discount = fields.Float(string='Discount (%)',
                            digits='Discount', default=0.0)

    is_added = fields.Boolean()

    to_so = fields.Boolean()

    display_type = fields.Selection([
        ('line_section', "Section"),
        ('line_note', "Note")], default=False, help="Technical field for UX purpose.")

    product_no_variant_attribute_value_ids = fields.Many2many(
        comodel_name='product.template.attribute.value',
        string="Extra Values",
        compute='_compute_no_variant_attribute_values',
        store=True, readonly=False, precompute=True, ondelete='restrict')
    
    tag_ids = fields.Many2many(
        comodel_name='sol.tag',
        string="Tags")
    
    @api.onchange('tag_ids')
    def _onchange_tag_ids(self):
        for line in self:
            if line.product_id.update_qty == True:
                line.product_uom_qty = len(line.tag_ids)

    usd_price_unit = fields.Float()

    @api.onchange('usd_price_unit')
    def _onchange_usd_amount(self):
        for rec in self:
            # TODO: check line and get usd rate
            usd_id = self.env.ref('base.USD')

            date = fields.Date.today()
            price_amt = usd_id._convert(rec.usd_price_unit, rec.currency_id, rec.company_id, date)

            rec.price_unit = price_amt

    def _update_usd_amount(self):
        for rec in self:
            price = 0
            if rec.crm_id.usd_pricelist_id and not rec.display_type:
                price, rule_id = rec.crm_id.usd_pricelist_id._get_product_price_rule(rec.product_id, rec.product_uom_qty, rec.product_uom, fields.Date.today())
            rec.usd_price_unit = price
            rec._onchange_usd_amount()
            

    @api.constrains('discount')
    def _check_discount(self):
        for line in self:
            if line.discount:
                discount_amt = 0
                for group in self.env['sales.discount.limit'].search([]):
                    if self.env.user in group.user_ids:
                        if discount_amt and group.discount < discount_amt:
                            continue
                        else:
                            discount_amt = group.discount


                        # break

                if discount_amt and line.discount > discount_amt and not self.env.user.has_group('base.group_system')\
                    and not self.env.user.has_group('sale_discount_limit.group_manager_disocunt_pers'):
                    raise UserError(
                        _('You are only eligible to give discounts up to'
                        ' %s .' % discount_amt))


    @api.depends('product_id')
    def _compute_no_variant_attribute_values(self):
        for line in self:
            if not line.product_id:
                line.product_no_variant_attribute_value_ids = False
                continue
            if not line.product_no_variant_attribute_value_ids:
                continue
            valid_values = line.product_id.product_tmpl_id.valid_product_template_attribute_line_ids.product_template_value_ids
            # remove the no_variant attributes that don't belong to this template
            for ptav in line.product_no_variant_attribute_value_ids:
                if ptav._origin not in valid_values:
                    line.product_no_variant_attribute_value_ids -= ptav

    @api.depends('product_id', 'product_uom', 'product_uom_qty')
    def _compute_price_unit(self):
        for line in self:
            # check if there is already invoiced amount. if so, the price shouldn't change as it might have been
            # manually edited
            if not line.product_uom or not line.product_id or not line.crm_id.pricelist_id:
                line.price_unit = 0.0
            else:
                price = line.with_company(line.company_id)._get_display_price()
                line.price_unit = line.product_id._get_tax_included_unit_price(
                    line.company_id,
                    line.crm_id.company_currency,
                    fields.Date.today(),
                    'sale',
                    fiscal_position=line.crm_id.fiscal_position_id,
                    product_price_unit=price,
                    product_currency=line.currency_id
                )
                #USD
                if line.crm_id.usd_pricelist_id:
                    line._update_usd_amount()


    @api.depends('product_id', 'product_uom', 'product_uom_qty')
    def _compute_pricelist_item_id(self):
        for line in self:
            if not line.product_id or line.display_type or not line.crm_id.pricelist_id:
                line.pricelist_item_id = False
            else:
                line.pricelist_item_id = line.crm_id.pricelist_id._get_product_rule(
                    line.product_id,
                    line.product_uom_qty or 1.0,
                    uom=line.product_uom,
                    date=fields.Date.today(),
                )

    @api.depends('product_uom_qty', 'discount', 'price_unit', 'tax_id')
    def _compute_amount(self):
        """
        Compute the amounts of the CRM line.
        """
        for line in self:
            price = line.price_unit * (1 - (line.discount or 0.0) / 100.0)
            taxes = line.tax_id.compute_all(
                price, self.currency_id, line.product_uom_qty, product=line.product_id, partner=line.crm_id.partner_id)
            line.update({
                'price_tax': taxes['total_included'] - taxes['total_excluded'],
                'price_total': taxes['total_included'],
                'price_subtotal': taxes['total_excluded'],
            })

    @api.onchange('product_id')
    def product_id_change(self):
        if not self.product_id:
            return

        vals = {}
        if not self.product_uom or (self.product_id.uom_id.id != self.product_uom.id):
            vals['product_uom'] = self.product_id.uom_id
            vals['product_uom_qty'] = self.product_uom_qty or 1.0

        product = self.product_id.with_context(
            lang=get_lang(self.env, self.crm_id.partner_id.lang).code,
            partner=self.crm_id.partner_id,
            quantity=vals.get('product_uom_qty') or self.product_uom_qty,
            date=self.crm_id.date_open,
            pricelist=self.crm_id.pricelist_id and self.crm_id.pricelist_id.id,
            uom=self.product_uom.id
        )

        vals.update(
            name=self.get_sale_order_line_multiline_description_sale(product))

        self._compute_tax_id()

        self.update(vals)

        if product.sale_line_warn != 'no-message':
            if product.sale_line_warn == 'block':
                self.product_id = False

            return {
                'warning': {
                    'title': _("Warning for %s", product.name),
                    'message': product.sale_line_warn_msg,
                }
            }

    def _get_display_price(self):
        """Compute the displayed unit price for a given line.

        Overridden in custom flows:
        * where the price is not specified by the pricelist
        * where the discount is not specified by the pricelist

        Note: self.ensure_one()
        """
        self.ensure_one()

        pricelist_price = self._get_pricelist_price()

        if self.crm_id.pricelist_id.discount_policy == 'with_discount':
            return pricelist_price

        if not self.pricelist_item_id:
            # No pricelist rule found => no discount from pricelist
            return pricelist_price

        base_price = self._get_pricelist_price_before_discount()

        # negative discounts (= surcharge) are included in the display price
        return max(base_price, pricelist_price)
    
    def _get_pricelist_price(self):
        """Compute the price given by the pricelist for the given line information.

        :return: the product sales price in the order currency (without taxes)
        :rtype: float
        """
        self.ensure_one()
        self.product_id.ensure_one()

        pricelist_rule = self.pricelist_item_id
        order_date = fields.Date.today()
        product = self.product_id.with_context(**self._get_product_price_context())
        qty = self.product_uom_qty or 1.0
        uom = self.product_uom or self.product_id.uom_id

        price = pricelist_rule._compute_price(
            product, qty, uom, order_date, currency=self.currency_id)

        return price

    def _get_product_price_context(self):
        """Gives the context for product price computation.

        :return: additional context to consider extra prices from attributes in the base product price.
        :rtype: dict
        """
        self.ensure_one()
        res = {}

        # It is possible that a no_variant attribute is still in a variant if
        # the type of the attribute has been changed after creation.
        no_variant_attributes_price_extra = [
            ptav.price_extra for ptav in self.product_no_variant_attribute_value_ids.filtered(
                lambda ptav:
                    ptav.price_extra and
                    ptav not in self.product_id.product_template_attribute_value_ids
            )
        ]
        if no_variant_attributes_price_extra:
            res['no_variant_attributes_price_extra'] = tuple(no_variant_attributes_price_extra)

        return res

    def _get_pricelist_price_before_discount(self):
        """Compute the price used as base for the pricelist price computation.

        :return: the product sales price in the order currency (without taxes)
        :rtype: float
        """
        self.ensure_one()
        self.product_id.ensure_one()

        pricelist_rule = self.pricelist_item_id
        order_date = fields.Date.today()
        product = self.product_id.with_context(**self._get_product_price_context())
        qty = self.product_uom_qty or 1.0
        uom = self.product_uom

        if pricelist_rule:
            pricelist_item = pricelist_rule
            if pricelist_item.pricelist_id.discount_policy == 'without_discount':
                # Find the lowest pricelist rule whose pricelist is configured
                # to show the discount to the customer.
                while pricelist_item.base == 'pricelist' and pricelist_item.base_pricelist_id.discount_policy == 'without_discount':
                    rule_id = pricelist_item.base_pricelist_id._get_product_rule(
                        product, qty, uom=uom, date=order_date)
                    pricelist_item = self.env['product.pricelist.item'].browse(rule_id)

            pricelist_rule = pricelist_item

        price = pricelist_rule._compute_base_price(
            product,
            qty,
            uom,
            order_date,
            target_currency=self.currency_id,
        )

        return price



    def _compute_tax_id(self):
        for line in self:
            line = line.with_company(line.company_id)
            fpos = self.env['account.fiscal.position'].with_company(
                self.company_id)._get_fiscal_position(line.crm_id.partner_id, line.crm_id.partner_id)
            # If company_id is set, always filter taxes by the company
            taxes = line.product_id.taxes_id.filtered(
                lambda t: t.company_id == line.env.company)
            line.tax_id = fpos.map_tax(taxes)

    def get_sale_order_line_multiline_description_sale(self, product):
        """ Compute a default multiline description for this sales order line.

        In most cases the product description is enough but sometimes we need to append information that only
        exists on the sale order line itself.
        e.g:
        - custom attributes and attributes that don't create variants, both introduced by the "product configurator"
        - in event_sale we need to know specifically the sales order line as well as the product to generate the name:
          the product is not sufficient because we also need to know the event_id and the event_ticket_id (both which belong to the sale order line).
        """
        return product.get_product_multiline_description_sale()


class CrmLead(models.Model):
    _inherit = 'crm.lead'

    contruct_date = fields.Datetime()

    customer_ref = fields.Char(related='partner_id.patient_id')

    crm_line = fields.One2many('crm.lead.line', 'crm_id')

    show_update_pricelist = fields.Boolean(string='Has Pricelist Changed',
                                           help="Technical Field, True if the pricelist was changed;\n"
                                                " this will then display a recomputation button")

    pricelist_id = fields.Many2one(
        'product.pricelist', string='Pricelist',
        required=True,
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]", tracking=1,
        help="If you change the pricelist, only newly added lines will be affected.")

    payment_term_id = fields.Many2one('account.payment.term')


    def get_domain_usd(self):
        return [('currency_id', '=', self.env.ref('base.USD').id), '|', ('company_id', '=', False), ('company_id', '=', self.env.company)]
    usd_pricelist_id = fields.Many2one('product.pricelist', domain=get_domain_usd,
        tracking=1,store=True, readonly=False)

    @api.onchange('usd_pricelist_id')
    def _onchange_usd_pricelist(self):
        self.crm_line._update_usd_amount()



    def recompute_total_usd_amount(self):
        for rec in self:
            if rec.usd_pricelist_id:
                not_downpayments = rec.crm_line

                total_down_payment_usd = 0
                # sum(downpayments.mapped('usd_price_unit'))
                total_down_payment_eg = 0
                # sum(downpayments.mapped('price_unit'))

                total_amt_usd = sum(not_downpayments.mapped(lambda x: x.usd_price_unit * x.product_uom_qty))

                total_amt_eg = sum(not_downpayments.mapped('price_subtotal'))


                remaining_usd = total_amt_usd - total_down_payment_usd


                usd_id = self.env.ref('base.USD')
                date = fields.Date.today()
                price_amt = usd_id._convert(remaining_usd, rec.currency_id, rec.company_id, date)

                new_total_eg = price_amt + total_down_payment_eg

                to_split = new_total_eg - total_amt_eg

                factor = (to_split / total_amt_eg) if total_amt_eg else 0

                for line in not_downpayments:
                        line.price_unit = line.price_unit + (factor * line.price_unit)





    @api.onchange('partner_id')
    def _onchange_partner_id_pricelist(self):
        self.pricelist_id = self.partner_id.property_product_pricelist
        self.payment_term_id = self.partner_id.property_payment_term_id

    # distance_amt = fields.Float()
    # place = fields.Char()
    fiscal_position_id = fields.Many2one(
        comodel_name='account.fiscal.position',
        string="Fiscal Position",
        compute='_compute_fiscal_position_id',
        store=True, readonly=False, precompute=True, check_company=True,
        help="Fiscal positions are used to adapt taxes and accounts for particular customers or sales orders/invoices."
            "The default value comes from the customer.",
        domain="[('company_id', '=', company_id)]")

    @api.depends('partner_id', 'company_id')
    def _compute_fiscal_position_id(self):
        """
        Trigger the change of fiscal position when the shipping address is modified.
        """
        cache = {}
        for order in self:
            if not order.partner_id:
                order.fiscal_position_id = False
                continue
            key = (order.company_id.id, order.partner_id.id, order.partner_id.id)
            if key not in cache:
                cache[key] = self.env['account.fiscal.position'].with_company(
                    order.company_id
                )._get_fiscal_position(order.partner_id, order.partner_id)
            order.fiscal_position_id = cache[key]


    def action_new_quotation(self):

        result = super(CrmLead, self).action_new_quotation()

        # if result.get('context'):
        #     result['context']['default_crm_sale_flag'] = True
        #     # result['context']['default_distance_amt'] = self.distance_amt
        #     # result['context']['default_place'] = self.place
        #     # result['context']['default_client_order_ref'] = self.place
        #     result['context']['default_payment_term_id'] = self.payment_term_id.id
        #     result['context']['default_pricelist_id'] = self.pricelist_id.id
        lines = []
        for this in self.crm_line.filtered(lambda x: x.to_so and not x.is_added):
            this.to_so = False 
            this.is_added = True

            lines.append((0,0, {
                'display_type': this.display_type,
                'sequence': this.sequence,
                'product_id': this.product_id.id,
                'name': this.name,
                'product_uom_qty': this.product_uom_qty,
                'product_uom': this.product_id.uom_id.id,
                'price_unit': this.price_unit,
                'usd_price_unit': this.usd_price_unit,
                'tax_id': [(6, 0, this.tax_id.ids)],
                'tag_ids': [(6, 0, this.tag_ids.ids)],
                'discount': this.discount,
                # 'company_id': this.company_id.id,
            }))
        # result['context']['default_order_line'] = lines

        action = False
        if len(lines)>0:
            quotation = self.env['sale.order'].create({
                'opportunity_id': self.id,
                'partner_id': self.partner_id.id,
                'campaign_id': self.campaign_id.id,
                'medium_id': self.medium_id.id,
                'source_id': self.source_id.id,
                'origin': self.name,
                'company_id': self.company_id.id or self.env.company.id,
                'tag_ids': [(6, 0, self.tag_ids.ids)],
                'team_id': self.team_id.id or False,
                'user_id': self.user_id.id or False,
                'crm_sale_flag': True,
                'payment_term_id': self.payment_term_id.id,
                'pricelist_id': self.pricelist_id.id,
                'usd_pricelist_id': self.usd_pricelist_id.id,
                'order_line': lines
            })


            action = self.env["ir.actions.actions"]._for_xml_id("sale.action_quotations_with_onboarding")
            action['views'] = [(self.env.ref('sale.view_order_form').id, 'form')]
            action['res_id'] = quotation.id

        # print(s)
        return action

    amount_untaxed = fields.Monetary(string='Untaxed Amount', store=True,
                                     compute='_amount_all', tracking=5, currency_field='company_currency')
    amount_tax = fields.Monetary(
        string='Taxes', store=True, compute='_amount_all', currency_field='company_currency')
    amount_total = fields.Monetary(
        string='Total', store=True, compute='_amount_all', tracking=4, currency_field='company_currency')

    @api.depends('crm_line.price_total')
    def _amount_all(self):
        """
        Compute the total amounts of the SO.
        """
        for order in self:
            amount_untaxed = amount_tax = 0.0
            for line in order.crm_line:
                amount_untaxed += line.price_subtotal
                amount_tax += line.price_tax
            order.update({
                'amount_untaxed': amount_untaxed,
                'amount_tax': amount_tax,
                'amount_total': amount_untaxed + amount_tax,
            })

    tax_totals_json = fields.Char(compute='_compute_tax_totals_json')

    @api.depends('crm_line.tax_id', 'crm_line.price_unit', 'amount_total', 'amount_untaxed')
    def _compute_tax_totals_json(self):
        def compute_taxes(crm_line):
            price = crm_line.price_unit * \
                (1 - (crm_line.discount or 0.0) / 100.0)
            order = crm_line.crm_id
            return crm_line.tax_id._origin.compute_all(price, order.company_currency, crm_line.product_uom_qty, product=crm_line.product_id, partner=order.partner_id)

        account_move = self.env['account.move']
        for order in self:
            tax_lines_data = account_move._prepare_tax_lines_data_for_totals_from_object(
                order.crm_line, compute_taxes)
            tax_totals = account_move._get_tax_totals(
                order.partner_id, tax_lines_data, order.amount_total, order.amount_untaxed, order.company_currency)
            order.tax_totals_json = json.dumps(tax_totals)

    @api.onchange('pricelist_id', 'crm_line')
    def _onchange_pricelist_id(self):
        if self.crm_line and self.pricelist_id and self._origin.pricelist_id != self.pricelist_id:
            self.show_update_pricelist = True
        else:
            self.show_update_pricelist = False

    def update_prices(self):
        self.ensure_one()
        # lines_to_update = []
        for line in self.crm_line.filtered(lambda line: not line.display_type):
            line._compute_price_unit()
        #     product = line.product_id.with_context(
        #         partner=self.partner_id,
        #         quantity=line.product_uom_qty,
        #         # date=self.date_order,
        #         pricelist=self.pricelist_id.id,
        #         uom=line.product_uom.id
        #     )
        #     price_unit = self.env['account.tax']._fix_tax_included_price_company(
        #         line._get_display_price(product), line.product_id.taxes_id, line.tax_id, line.company_id)
        #     if self.pricelist_id.discount_policy == 'without_discount' and price_unit:
        #         discount = max(0, (price_unit - product.list_price)
        #                        * 100 / price_unit)
        #     else:
        #         discount = 0
        #     lines_to_update.append(
        #         (1, line.id, {'price_unit': price_unit, 'discount': discount}))
        # self.update({'crm_line': lines_to_update})
        self.show_update_pricelist = False
        self.message_post(body=_(
            "Product prices have been recomputed according to pricelist <b>%s<b> ", self.pricelist_id.display_name))

