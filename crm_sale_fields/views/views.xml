<odoo>
  <data>
    <record id="crm_lead_view_form_inherit" model="ir.ui.view">
      <field name="name">CRM Lead Products</field>
      <field name="model">crm.lead</field>
      <field name="inherit_id" ref="crm.crm_lead_view_form"/>
      <field name="arch" type="xml">
        <xpath expr="//group[@name='opportunity_partner']/field[@name='partner_id']" position="after">
          <field name="customer_ref" />
          
          <field name="payment_term_id" />

          <label for="pricelist_id"/>
          <div groups="product.group_product_pricelist" class="o_row">
              <field name="pricelist_id" options="{'no_open':True,'no_create': True}"/>
              <button name="update_prices" type="object"
                  string=" Update Prices"
                  help="Recompute all prices based on this pricelist"
                  class="btn-link mb-1 px-0" icon="fa-refresh"
                  confirm="This will update all unit prices based on the currently set pricelist."/>
          </div>
          <field name="usd_pricelist_id" options="{'no_open':True,'no_create': True}"/>
          
          <!-- <field name="place" />
          <label for="distance_amt" />
          <div class="o_row">
              <field name="distance_amt" />
              KM
          </div> -->

        </xpath>
        <xpath expr="//group[@name='lead_partner']/field[@name='partner_id']" position="after">

          <label for="pricelist_id"/>
          <div groups="product.group_product_pricelist" class="o_row">
              <field name="pricelist_id" options="{'no_open':True,'no_create': True}"/>
              <button name="update_prices" type="object"
                  string=" Update Prices"
                  help="Recompute all prices based on this pricelist"
                  class="btn-link mb-1 px-0" icon="fa-refresh"
                  confirm="This will update all unit prices based on the currently set pricelist."/>
          </div>
          <field name="usd_pricelist_id" options="{'no_open':True,'no_create': True}"/>
          
          <!-- <field name="place" />
          <label for="distance_amt" />
          <div class="o_row">
              <field name="distance_amt" />
              KM
          </div> -->

        </xpath>

        <xpath expr="//field[@name='user_id']" position="after">
          <field name="contruct_date" />
        </xpath>

        <xpath expr="//page[@name='internal_notes']" position="before">
          <page string="Order Lines" name="order_lines">
            <field name="crm_line" widget="section_and_note_one2many" mode="tree,kanban" >
              <!-- attrs="{'readonly': [('state', 'in', ('done','cancel'))]}"                 -->
              <list string="Sales Order Lines" editable="bottom">
                  <control>
                      <create name="add_product_control" string="Add a product"/>
                      <create name="add_section_control" string="Add a section" context="{'default_display_type': 'line_section'}"/>
                      <create name="add_note_control" string="Add a note" context="{'default_display_type': 'line_note'}"/>
                  </control>

                  <field name="sequence" widget="handle"/>
                  <!-- We do not display the type because we don't want the user to be bothered with that information if he has no section or note. -->
                  <field name="display_type" invisible="1"/>
                  <field name="product_uom_category_id" invisible="1"/>

                  <field name="product_id" attrs="{'required': [('display_type', '=', False)],}" force_save="1" context="{'partner_id': parent.partner_id,'quantity': product_uom_qty,'uom':product_uom,'company_id': parent.company_id,                                             'default_lst_price': price_unit,                                             'default_description_sale': name                                         }" domain="[('sale_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]" widget="product_configurator"/>
                  <field name="name" widget="section_and_note_text" optional="show"/>
                  <field name="product_uom_qty" context="{'partner_id': parent.partner_id, 'quantity': product_uom_qty, 'uom': product_uom, 'company_id': parent.company_id}"/>
                  <field name="product_uom" invisible="1" groups="!uom.group_uom"/>
                  <field name="product_uom" force_save="1" string="UoM" attrs="{'required': [('display_type', '=', False)],}" context="{'company_id': parent.company_id}" groups="uom.group_uom" options="{&quot;no_open&quot;: True}" optional="show"/>
                  <field name="tag_ids"  widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}"/>
                  <!-- <field name="customer_lead" optional="hide" attrs="{'readonly': [('parent.state', 'not in', ['draft', 'sent', 'sale'])]}"/> -->
                  <field name="price_unit" />
                  <field name="usd_price_unit" />
                  <!-- attrs="{'readonly': [('qty_invoiced', '&gt;', 0)]}"/> -->
                  <field name="tax_id" widget="many2many_tags" options="{'no_create': True}" domain="[('type_tax_use','=','sale'),('company_id','=',parent.company_id)]" context="{'active_test': True}" optional="show"/>
                  <field name="discount" string="Disc.%" groups="product.group_discount_per_so_line" optional="show" widget="product_discount"/>
                  <field name="price_subtotal" widget="monetary" groups="account.group_show_line_subtotals_tax_excluded"/>
                  <field name="price_total" widget="monetary" groups="account.group_show_line_subtotals_tax_included"/>
                  <field name="company_id" invisible="1"/>
                  <field name="to_so" widget="boolean_toggle"/>
                  <field name="is_added" optional='show' readonly="1" force_save="1"/>
              </list>
            </field>

            
          </page>
        </xpath>
      </field>
    </record>


    <record id="sale_order_place_view_form_inherit" model="ir.ui.view">
      <field name="name">Sale place distance</field>
      <field name="model">sale.order</field>
      <field name="inherit_id" ref="sale.view_order_form"/>
      <field name="arch" type="xml">
        <xpath expr="//group[@name='partner_details']" position="inside">
          <field name="customer_ref" />
          <!-- <label for="distance_amt" />
          <div class="o_row">
            <field name="distance_amt" />
            KM
          </div>
          <field name="place" /> -->
          <field name="crm_sale_flag" invisible='1'/>
        </xpath>

        <xpath expr="//field[@name='payment_term_id']" position="attributes">
          <attribute name="readonly">1</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='payment_term_id']" position="attributes">
          <attribute name="attrs">{'readonly':['|',('state', 'not in', ['draft', 'sent']),('opportunity_id','!=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>

        <xpath expr="//field[@name='analytic_account_id']" position="attributes">
          <attribute name="attrs">{'readonly':[('opportunity_id','!=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <!-- <xpath expr="//field[@name='order_line']" position="attributes">
          <attribute name="attrs">{'readonly':[('opportunity_id','!=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath> -->
        
        <xpath expr="//field[@name='order_line']//tree//field[@name='price_unit']" position="attributes">
          <attribute name="attrs">{'readonly':['|','|',('parent.crm_sale_flag','=',True),('is_sale_manager','=',False),('qty_invoiced','>',0)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='order_line']//tree//field[@name='usd_price_unit']" position="attributes">
          <attribute name="attrs">{'readonly':['|','|',('parent.crm_sale_flag','=',True),('is_sale_manager','=',False),('qty_invoiced','>',0)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>

        <xpath expr="//field[@name='order_line']//tree//field[@name='product_id']" position="attributes">
          <attribute name="attrs">{'readonly':['|',('parent.opportunity_id','!=',False),('product_updatable','=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='order_line']//tree//field[@name='product_template_id']" position="attributes">
          <attribute name="attrs">{'readonly':['|',('parent.opportunity_id','!=',False),('product_updatable','=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>

        <xpath expr="//field[@name='order_line']//tree//field[@name='name']" position="attributes">
          <attribute name="attrs">{'readonly':[('parent.opportunity_id','!=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='order_line']//tree//field[@name='route_id']" position="attributes">
          <attribute name="attrs">{'readonly':[('parent.opportunity_id','!=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='order_line']//tree//field[@name='price_unit']" position="attributes">
          <attribute name="attrs">{'readonly':[('parent.opportunity_id','!=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='order_line']//tree//field[@name='product_uom_qty']" position="attributes">
          <attribute name="attrs">{'readonly':[('parent.opportunity_id','!=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='order_line']//tree//field[@name='product_uom']" position="attributes">
          <attribute name="attrs">{'readonly':['|',('parent.opportunity_id','!=',False),('product_uom_readonly','=',True)],'required':[('display_type','=',False)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='order_line']//tree//field[@name='tax_id']" position="attributes">
          <attribute name="attrs">{'readonly':['|',('parent.opportunity_id','!=',False), ('qty_invoiced', '&gt;', 0)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>
        <xpath expr="//field[@name='order_line']//tree//field[@name='discount']" position="attributes">
          <attribute name="attrs">{'readonly':['|',('parent.opportunity_id','!=',False), ('qty_invoiced', '&gt;', 0)]}</attribute>
          <attribute name="force_save">1</attribute>
        </xpath>

      </field>
    </record>

    <!-- <record id="sale_order_form_credit_check_readonly" model="ir.ui.view">
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_order_customer_balance.egy_trade_sale_order_total_due_inherit"/>
        <field name="arch" type="xml">
          <xpath expr="//field[@name='credit_check']" position="attributes">
            <attribute name="attrs">{'readonly':[('opportunity_id','=',False)]}</attribute>
            <attribute name="force_save">1</attribute>
          </xpath>
        </field>
    </record>
 -->
  </data>
</odoo>