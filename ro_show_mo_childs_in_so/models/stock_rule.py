from odoo import api, models, fields, _
from odoo.exceptions import UserError 
class StockRule(models.Model):
    _inherit = 'stock.rule'
    
    def _get_custom_move_fields(self):
        fields = super(StockRule, self)._get_custom_move_fields()
        fields += ['ro_desc']
        fields+= ['ro_tags']
        return fields

    def _prepare_mo_vals(self, product_id, product_qty, product_uom, location_dest_id, name, origin, company_id, values, bom):
        res = super()._prepare_mo_vals(product_id, product_qty, product_uom, location_dest_id, name, origin, company_id, values, bom)
        if values.get('ro_desc'):
            res['ro_product_desc'] =  values.get('ro_desc')
        if values.get('ro_tags'):
            res['ro_product_tags'] =  values.get('ro_tags')
            
            
        return res
    