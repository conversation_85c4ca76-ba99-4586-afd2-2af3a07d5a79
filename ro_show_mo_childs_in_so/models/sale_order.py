
from odoo import models,fields,_,api

class SaleOrder(models.Model):
    _inherit = "sale.order"
    
    ro_childs_count = fields.Integer(string='Childs Count', compute='_compute_childs_count')

    
    
    def _compute_mrp_production_child_ids(self):
        def recu(mo):
            childrens = mo._get_children()
            if not childrens:  
                return mo

            result = mo
            for child in childrens:
                result += recu(child)  
            return result
        all_mo_childs= self.env['mrp.production']   
        for order in self:
            parents_mo = order.mrp_production_ids
            for parent in parents_mo: 
                mo_childs = recu(parent)
                if mo_childs:
                    mo_childs = mo_childs[1:]
                    all_mo_childs |= mo_childs
        
                
        return all_mo_childs
         
    
    def action_view_childs(self):
        self.ensure_one()
        childs =  self._compute_mrp_production_child_ids()
        result = self.env['ir.actions.act_window']._for_xml_id('mrp.mrp_production_action')
        if len(childs) > 1:
            result['domain'] = [('id', 'in', childs.ids)]
        elif len(childs) == 1:
            result['views'] = [(self.env.ref('mrp.mrp_production_form_view', False).id, 'form')]
            result['res_id'] = childs.id
        else:
            result = {'type': 'ir.actions.act_window_close'}
        return result
   
   
    def _compute_childs_count(self):
        for record in self:
            record.ro_childs_count = len(self._compute_mrp_production_child_ids())
    