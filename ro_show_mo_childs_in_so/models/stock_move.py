from odoo import api, models, fields, _


class StockMove(models.Model):
    _inherit = 'stock.move'

    ro_desc = fields.Char(string="Description")
    ro_tags=fields.Many2many(comodel_name='sol.tag',string="Tags")

    def _prepare_procurement_values(self):
        res = super()._prepare_procurement_values()
        res['ro_desc'] = self.sale_line_id.name or self.move_dest_ids.sale_line_id.name\
            or self.move_dest_ids.move_dest_ids.sale_line_id.name or self.raw_material_production_id.ro_product_desc
            
        res['ro_tags'] =self.sale_line_id.tag_ids.ids or self.move_dest_ids.sale_line_id.tag_ids.ids\
            or self.move_dest_ids.move_dest_ids.sale_line_id.tag_ids.ids or self.raw_material_production_id.ro_product_tags
            
        
        return res
