# -*- coding: utf-8 -*-
{
    'name': "Show MO Childs In SO",

    'summary': """
        smart bottom in SO to show all child Mo (Lab)""",

    'description': """
        smart bottom in SO to show all child Mo(Lab)
    """,

    'author': "Roaya",
    'website': "https://www.roayadm.com",

    'category': 'sale',
    'version': '16.0',

    'depends': ['sale','mrp','sale_mrp','sale_stock','stock','add_tags_in_so_line','ro_so_mo_customer_ref'],

    'data': [
        'views/sale_order.xml',
        'views/mrp_production.xml'
        
    ],
    'installable': True,
    'license': 'OPL-1',
   
}
