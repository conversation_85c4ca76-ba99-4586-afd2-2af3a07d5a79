from odoo import models, api,fields,_
from odoo.exceptions import UserError


class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    is_so_and_partner = fields.Boolean(
        compute='_compute_is_so_and_partner' )
    
    
   
    def button_mark_done(self):
        for production in self:
            lot_serial_number = production._generate_lot_serial_number()  
            if lot_serial_number:
                production.write({'lot_producing_id': lot_serial_number.id})
        return super().button_mark_done()

    def _generate_lot_serial_number(self):
        for production in self:
            lot_concatenate = ''
            if production.ro_sale_id and production.ro_partner_id:
                lot_concatenate= production.ro_sale_id.name + ' - '+ production.ro_partner_id.name
            if lot_concatenate:
                # print("lot_concatenate",lot_concatenate)
                founded_lot=self.env['stock.lot'].search([
                    ('name','=',lot_concatenate),('product_id','=',production.product_id.id) ],limit=1)  
                if founded_lot:
                    return founded_lot
                else:
                    lot = self.env['stock.lot'].sudo().create({
                        'name': lot_concatenate,
                        'product_id': production.product_id.id,
                        'product_qty':production.product_qty
                    })
                    return lot 
        return False
    
    
    @api.depends('lot_producing_id')
    def _compute_is_so_and_partner(self):
        for record in self:
            # //law el 2 mawgdeen 7y7sb mnhm el lot w y5leh readonly
                if record.ro_sale_id and record.ro_partner_id:
                    record.is_so_and_partner=True
                else:
                    record.is_so_and_partner=False
                    
    
    
   