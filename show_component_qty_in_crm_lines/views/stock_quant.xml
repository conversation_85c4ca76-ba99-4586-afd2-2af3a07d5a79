<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_stock_quant_tree_inventory_editable_update_qty_hide" model="ir.ui.view">
        <field name="model">stock.quant</field>
        <field eval="19" name="priority"/>
        <field name="inherit_id" ref="stock.view_stock_quant_tree_inventory_editable" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='inventory_quantity']" position="attributes">
                <attribute name="groups">show_component_qty_in_crm_lines.group_manager_update_qty</attribute>
            </xpath>
        </field>
    </record>

    <record id="product_template_form_view_button_update_qty" model="ir.ui.view">
        <field name="model">product.template</field>
        <field name="type">form</field>
        <field name="inherit_id" ref="stock.product_template_form_view_procurement_button" />
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_update_quantity_on_hand']" position="attributes">
                <attribute name="groups">show_component_qty_in_crm_lines.group_manager_update_qty</attribute>
            </xpath>
        </field>
    </record>
    <record model="ir.ui.menu" id="stock.menu_action_inventory_tree">
        <field name="groups_id" eval="[(4, ref('show_component_qty_in_crm_lines.group_manager_update_qty'))]"/>
    </record>
    
</odoo>