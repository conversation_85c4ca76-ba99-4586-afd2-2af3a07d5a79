from odoo import api, models, fields, _
from odoo.exceptions import UserError

class StockLocation(models.Model):

    _inherit = 'stock.location'

    # main_warehouse = fields.Boolean('Main Warehouse', default=False)
    # sub_warehouse = fields.Many2one('stock.warehouse', string='Sub Warehouse')


class Picking(models.Model):

    _inherit = 'stock.picking'

    def write(self, vals):
        res = super(Picking, self).write(vals)
        if vals.get('location_id') or vals.get('location_dest_id'):
            is_source_main = self.location_id.warehouse_id.lot_stock_id == self.location_id
            is_dest_main = self.location_dest_id.warehouse_id.lot_stock_id == self.location_dest_id
            if self.picking_type_code == 'internal':
                if self.location_id.warehouse_id and not self.location_dest_id.warehouse_id:
                    return res
                elif (is_source_main and is_dest_main) or self.location_id.warehouse_id == self.location_dest_id.warehouse_id:
                    return res
                else:
                    raise UserError(
                        _('The Transfer is not between the same Branch'))
            else:
                return res
        return res

